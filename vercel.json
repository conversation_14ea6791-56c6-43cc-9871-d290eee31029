{"version": 2, "outputDirectory": ".wasp/build/web-app/build", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": ".wasp/build/web-app/build"}}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/stripe-webhook", "destination": "/api/stripe-webhook"}, {"source": "/ln-login", "destination": "/api/ln-login"}], "cleanUrls": true, "trailingSlash": false, "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production", "WASP_ENV": "production"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}