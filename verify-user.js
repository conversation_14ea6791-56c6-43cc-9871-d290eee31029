import { PrismaClient } from '@prisma/client';

async function verifyUser() {
  const prisma = new PrismaClient();

  try {
    // Find the user by email with auth information
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        auth: {
          include: {
            identities: true
          }
        }
      }
    });

    if (!user) {
      console.log('User not found');
      return;
    }

    console.log('User found:', {
      id: user.id,
      email: user.email,
      username: user.username,
      auth: user.auth,
      identities: user.auth?.identities
    });

    // Check if there's an email identity
    const emailIdentity = user.auth?.identities?.find(
      identity => identity.providerName === 'email'
    );

    if (emailIdentity) {
      console.log('Email identity found:', emailIdentity);

      // Parse the provider data to check verification status
      const providerData = JSON.parse(emailIdentity.providerData);
      console.log('Provider data:', providerData);

      if (!providerData.isEmailVerified) {
        // Update the provider data to mark email as verified
        const updatedProviderData = {
          ...providerData,
          isEmailVerified: true
        };

        await prisma.authIdentity.update({
          where: {
            providerName_providerUserId: {
              providerName: 'email',
              providerUserId: emailIdentity.providerUserId
            }
          },
          data: {
            providerData: JSON.stringify(updatedProviderData)
          }
        });

        console.log('Email verification status updated successfully!');
      } else {
        console.log('Email is already verified');
      }
    } else {
      console.log('No email identity found');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyUser();
