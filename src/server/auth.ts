import { defineUserSignupFields } from 'wasp/server/auth';

// Helper function to generate a unique username (simplified for now)
function generateUsername(baseUsername: string): string {
  // Clean the base username: remove special characters, convert to lowercase
  let cleanUsername = baseUsername
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric characters
    .substring(0, 15); // Limit length

  // If the cleaned username is too short, add a default prefix
  if (cleanUsername.length < 3) {
    cleanUsername = 'user' + cleanUsername;
  }

  // Add a random suffix to reduce collision probability
  const randomSuffix = Math.floor(Math.random() * 10000).toString();
  return `${cleanUsername}${randomSuffix}`;
}

export const getUserFields = defineUserSignupFields({
  email: async (data: any) => {
    console.log('=== EMAIL FIELD PROCESSING ===');
    console.log('Received data:', JSON.stringify(data, null, 2));

    if (data.profile) {
      console.log('Google OAuth email:', data.profile.email);
      return data.profile.email;
    } else {
      console.log('Email/password signup email:', data.email);

      // Check if email already exists
      try {
        const { PrismaClient } = await import('@prisma/client');
        const prisma = new PrismaClient();

        const existingUser = await prisma.user.findUnique({
          where: { email: data.email }
        });

        if (existingUser) {
          console.error('Email already exists:', data.email);
          throw new Error('Email already exists');
        }

        console.log('Email is available:', data.email);
        await prisma.$disconnect();
      } catch (error: any) {
        console.error('Database check error:', error);
        if (error?.message === 'Email already exists') {
          throw error;
        }
        // Continue if it's just a database connection issue
      }

      return data.email;
    }
  },
  username: (data: any) => {
    console.log('=== USERNAME FIELD PROCESSING ===');
    console.log('Received data:', JSON.stringify(data, null, 2));

    if (data.profile) {
      const baseUsername = data.profile.name || data.profile.email.split('@')[0];
      const username = generateUsername(baseUsername);
      console.log('Google OAuth username generated:', username);
      return username;
    } else {
      const baseUsername = data.email.split('@')[0];
      const username = generateUsername(baseUsername);
      console.log('Email/password username generated:', username);
      return username;
    }
  }
})

export function config() {
  return {
    scopes: ['profile', 'email'],
  };
}

export function getVerificationEmailContent({ verificationLink }: { verificationLink: string }) {
  return {
    subject: 'Verify your CareerDart account',
    text: `Click the link below to verify your email address: ${verificationLink}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to CareerDart!</h2>
        <p>Thank you for signing up. Please verify your email address by clicking the button below:</p>
        <a href="${verificationLink}" style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
          Verify Email Address
        </a>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280;">${verificationLink}</p>
        <p>This link will expire in 24 hours.</p>
        <hr style="margin: 32px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          If you didn't create an account with CareerDart, you can safely ignore this email.
        </p>
      </div>
    `,
  };
}

export function getPasswordResetEmailContent({ passwordResetLink }: { passwordResetLink: string }) {
  return {
    subject: 'Reset your CareerDart password',
    text: `Click the link below to reset your password: ${passwordResetLink}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Reset Your Password</h2>
        <p>You requested to reset your password for your CareerDart account. Click the button below to set a new password:</p>
        <a href="${passwordResetLink}" style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 16px 0;">
          Reset Password
        </a>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280;">${passwordResetLink}</p>
        <p>This link will expire in 24 hours.</p>
        <hr style="margin: 32px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="color: #6b7280; font-size: 14px;">
          If you didn't request a password reset, you can safely ignore this email.
        </p>
      </div>
    `,
  };
}
