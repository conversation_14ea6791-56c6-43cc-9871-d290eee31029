import { type Job, type CoverLetter, type User, type LnPayment, type LearningProgress, type Badge, type UserBadge } from "wasp/entities";
import { HttpError } from "wasp/server";
import {
  type GenerateCoverLetter,
  type CreateJob,
  type UpdateCoverLetter,
  type EditCoverLetter,
  type GenerateEdit,
  type UpdateJob,
  type UpdateUser,
  type DeleteJob,
  type StripePayment,
  type StripeGpt4Payment,
  type StripeCreditsPayment,
  type ImportJobFromLinkedIn,
} from "wasp/server/operations";
import fetch from 'node-fetch';
import Stripe from 'stripe';

// Type definition for OpenAI API response is defined below

const stripe = new Stripe(process.env.STRIPE_KEY!, {
  apiVersion: '2023-08-16',
});

const DOMAIN = process.env.WASP_WEB_CLIENT_URL || 'http://localhost:3000';

const gptConfig = {
  completeCoverLetter: `You are a cover letter generator.
You will be given a job description along with the job applicant's resume.
You will write a cover letter for the applicant that matches their past experiences from the resume with the job description. Write the cover letter in the same language as the job description provided!
Rather than simply outlining the applicant's past experiences, you will give more detail and explain how those experiences will help the applicant succeed in the new job.
You will write the cover letter in a modern, professional style without being too formal, as a modern employee might do naturally.`,
  ideasForCoverLetter:
    "You are a cover letter idea generator. You will be given a job description along with the job applicant's resume. You will generate a bullet point list of ideas for the applicant to use in their cover letter. ",
};

type CoverLetterPayload = Pick<CoverLetter, 'title' | 'jobId'> & {
  content: string;
  description: string;
  isCompleteCoverLetter: boolean;
  temperature: number;
  gptModel: string;
  lnPayment?: LnPayment;
};

type OpenAIResponse = {
  id?: string;
  object?: string;
  created?: number;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  choices?: Array<{
    index?: number;
    message?: {
      role?: string;
      content: string;
    };
    finish_reason?: string;
  }>;
  error?: {
    message?: string;
  };
};

async function checkIfUserPaid({ context, lnPayment }: { context: any; lnPayment?: LnPayment }) {
  if (!context.user.hasPaid && !context.user.credits && !context.user.isUsingLn) {
    throw new HttpError(402, 'User must pay to continue');
  }
  if (context.user.subscriptionStatus === 'past_due') {
    throw new HttpError(402, 'Your subscription is past due. Please update your payment method.');
  }
  if (context.user.isUsingLn) {
    let invoiceStatus;
    if (lnPayment) {
      const lnPaymentInDB = await context.entities.LnPayment.findUnique({
        where: {
          pr: lnPayment.pr,
        },
      });
      invoiceStatus = lnPaymentInDB?.status;
    }
    console.table({ lnPayment, invoiceStatus });
    if (invoiceStatus !== 'success') {
      throw new HttpError(402, 'Your lightning payment has not been paid');
    }
  }
}

export const generateCoverLetter: GenerateCoverLetter<CoverLetterPayload, CoverLetter> = async (
  { jobId, title, content, description, isCompleteCoverLetter, temperature, gptModel, lnPayment },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  await checkIfUserPaid({ context, lnPayment })

  let command;
  if (isCompleteCoverLetter) {
    command = gptConfig.completeCoverLetter;
  } else {
    command = gptConfig.ideasForCoverLetter;
  }

  console.log(' gpt model: ', gptModel);

  const payload = {
    model: gptModel,
    messages: [
      {
        role: 'system',
        content: command,
      },
      {
        role: 'user',
        content: `My Resume: ${content}. Job title: ${title} Job Description: ${description}.`,
      },
    ],
    temperature,
  };

  let json: OpenAIResponse;

  try {
    if (!context.user.hasPaid && !context.user.credits && !context.user.isUsingLn) {
      throw new HttpError(402, 'User has not paid or is out of credits');
    } else if (context.user.credits && !context.user.hasPaid) {
      console.log('decrementing credits \n\n');
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            decrement: 1,
          },
        },
      });
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
      },
      method: 'POST',
      body: JSON.stringify(payload),
    });

    json = (await response.json()) as OpenAIResponse;

    if (json?.error) throw new HttpError(500, json?.error?.message || 'Something went wrong');

    return context.entities.CoverLetter.create({
      data: {
        title,
        content: json?.choices[0].message.content,
        tokenUsage: json?.usage.completion_tokens,
        user: { connect: { id: context.user.id } },
        job: { connect: { id: jobId } },
      },
    });
  } catch (error: any) {
    if (!context.user.hasPaid && error?.statusCode != 402) {
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            increment: 1,
          },
        },
      });
    }
    console.error(error);
    throw new HttpError(error.statusCode || 500, error.message || 'Something went wrong');
  }
};

export const generateEdit: GenerateEdit<
  { content: string; improvement: string; lnPayment?: LnPayment },
  string
> = async ({ content, improvement, lnPayment }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  await checkIfUserPaid({ context, lnPayment });

  let command;
  command = `You are a cover letter editor. You will be given a piece of isolated text from within a cover letter and told how you can improve it. Only respond with the revision. Make sure the revision is in the same language as the given isolated text.`;

  const payload = {
    model: context.user.gptModel === 'gpt-4' || context.user.gptModel === 'gpt-4o' ? 'gpt-4o' : 'gpt-4o-mini',
    messages: [
      {
        role: 'system',
        content: command,
      },
      {
        role: 'user',
        content: `Isolated text from within cover letter: ${content}. It should be improved by making it more: ${improvement}`,
      },
    ],
    temperature: 0.5,
  };

  let json: OpenAIResponse;

  try {
    if (!context.user.hasPaid && !context.user.credits && !context.user.isUsingLn) {
      throw new HttpError(402, 'User has not paid or is out of credits');
    } else if (context.user.credits && !context.user.hasPaid) {
      console.log('decrementing credits \n\n');
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            decrement: 1,
          },
        },
      });
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
      },
      method: 'POST',
      body: JSON.stringify(payload),
    });

    json = (await response.json()) as OpenAIResponse;
    if (json?.choices[0].message.content.length) {
      return json?.choices[0].message.content;
    } else {
      throw new HttpError(500, 'GPT returned an empty response');
    }
  } catch (error: any) {
    if (!context.user.hasPaid && error?.statusCode != 402) {
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            increment: 1,
          },
        },
      });
    }
    console.error(error);
    throw new HttpError(error.statusCode || 500, error.message || 'Something went wrong');
  }
};

export type JobPayload = Pick<Job, 'title' | 'company' | 'location' | 'description'>;

export const createJob: CreateJob<JobPayload, Job> = ({ title, company, location, description }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.Job.create({
    data: {
      title,
      description,
      location,
      company,
      user: { connect: { id: context.user.id } },
    },
  });
};

export type UpdateJobPayload = Pick<Job, 'id' | 'title' | 'company' | 'location' | 'description' | 'isCompleted'>;

export const updateJob: UpdateJob<UpdateJobPayload, Job> = (
  { id, title, company, location, description, isCompleted },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.Job.update({
    where: {
      id,
    },
    data: {
      title,
      description,
      location,
      company,
      isCompleted,
    },
  });
};

type LinkedInJobPayload = {
  url: string;
};

export const importJobFromLinkedIn: ImportJobFromLinkedIn<LinkedInJobPayload, JobPayload> = async (
  { url },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  // Validate LinkedIn URL
  if (!url.includes('linkedin.com/jobs/view/')) {
    throw new HttpError(400, 'Invalid LinkedIn job URL. Please provide a URL from linkedin.com/jobs/view/');
  }

  try {
    // Fetch the LinkedIn job page
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    if (!response.ok) {
      throw new HttpError(response.status, `Failed to fetch job data: ${response.statusText}`);
    }

    const html = await response.text();

    // Extract job title - LinkedIn uses h1 with specific classes
    const titleMatch = html.match(/<h1[^>]*class="[^"]*top-card-layout__title[^"]*"[^>]*>(.*?)<\/h1>/i) ||
                      html.match(/<h1[^>]*class="[^"]*job-title[^"]*"[^>]*>(.*?)<\/h1>/i) ||
                      html.match(/<h1[^>]*>(.*?)<\/h1>/i);
    const title = titleMatch ? cleanHtml(titleMatch[1]) : 'Unknown Position';

    // Extract company name - LinkedIn shows company in specific elements
    const companyMatch = html.match(/<a[^>]*class="[^"]*topcard__org-name-link[^"]*"[^>]*>(.*?)<\/a>/i) ||
                        html.match(/<span[^>]*class="[^"]*topcard__flavor[^"]*"[^>]*>(.*?)<\/span>/i) ||
                        html.match(/<a[^>]*class="[^"]*sub-nav-cta__optional-url[^"]*"[^>]*>(.*?)<\/a>/i) ||
                        html.match(/<span[^>]*class="[^"]*company-name[^"]*"[^>]*>(.*?)<\/span>/i) ||
                        html.match(/<a[^>]*class="[^"]*company[^"]*"[^>]*>(.*?)<\/a>/i);
    const company = companyMatch ? cleanHtml(companyMatch[1]) : 'Unknown Company';

    // Extract location - LinkedIn uses "Work Location" or similar patterns
    const locationMatch = html.match(/Work Location[^<]*<[^>]*>(.*?)<\/[^>]*>/i) ||
                         html.match(/Work Location.*?<span[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*topcard__flavor--bullet[^"]*"[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*job-criteria__text[^"]*"[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*topcard__flavor[^"]*"[^>]*>.*?·\s*(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*location[^"]*"[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*job-location[^"]*"[^>]*>(.*?)<\/span>/i);
    const location = locationMatch ? cleanHtml(locationMatch[1]) : 'Unknown Location';

    // Extract job description - LinkedIn uses specific description containers
    const descriptionMatch = html.match(/<div[^>]*class="[^"]*show-more-less-html__markup[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                            html.match(/<div[^>]*class="[^"]*description__text[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                            html.match(/<div[^>]*class="[^"]*description[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                            html.match(/<div[^>]*class="[^"]*job-description[^"]*"[^>]*>([\s\S]*?)<\/div>/i);
    const description = descriptionMatch ? cleanHtml(descriptionMatch[1]) : 'No description available';

    // If we couldn't extract the basic information, use OpenAI to help parse it
    if (title === 'Unknown Position' || company === 'Unknown Company' || description === 'No description available') {
      // Use OpenAI to extract job information from HTML
      const payload = {
        model: "gpt-4o-mini",
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that extracts job information from LinkedIn HTML. Focus on finding the job title, company name, work location, and job description. LinkedIn often shows location as "Work Location" and company names in topcard elements.',
          },
          {
            role: 'user',
            content: `Extract the job title, company name, work location, and full job description from this LinkedIn job posting HTML.

Key patterns to look for:
- Job title: Usually in h1 tags with "top-card-layout__title" or similar classes
- Company name: Often in "topcard__org-name-link" or "topcard__flavor" elements
- Location: Look for "Work Location" text or "topcard__flavor--bullet" elements
- Description: Usually in "show-more-less-html__markup" or "description__text" containers

Return the information in JSON format with keys: title, company, location, description.

HTML: ${html.substring(0, 15000)}...`,
          },
        ],
        response_format: { type: "json_object" },
      };

      const aiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
        },
        method: 'POST',
        body: JSON.stringify(payload),
      });

      if (!aiResponse.ok) {
        throw new HttpError(aiResponse.status, `Failed to parse job data: ${aiResponse.statusText}`);
      }

      const aiJson = await aiResponse.json() as OpenAIResponse;
      if (!aiJson.choices || !aiJson.choices[0] || !aiJson.choices[0].message) {
        throw new HttpError(500, 'Invalid response format from OpenAI');
      }
      const parsedData = JSON.parse(aiJson.choices[0].message.content);

      return {
        title: parsedData.title || title,
        company: parsedData.company || company,
        location: parsedData.location || location,
        description: parsedData.description || description,
      };
    }

    return {
      title,
      company,
      location,
      description,
    };
  } catch (error: any) {
    console.error('Error importing job from LinkedIn:', error);
    throw new HttpError(error.statusCode || 500, error.message || 'Failed to import job from LinkedIn');
  }
};

// Helper function to clean HTML content
function cleanHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with spaces
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/&lt;/g, '<') // Replace &lt; with <
    .replace(/&gt;/g, '>') // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .replace(/&#39;/g, "'") // Replace &#39; with '
    .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
    .trim(); // Remove leading/trailing whitespace
}

export type UpdateCoverLetterPayload = Pick<Job, 'id' | 'description'> &
  Pick<CoverLetter, 'content'> & {
    isCompleteCoverLetter: boolean;
    temperature: number;
    gptModel: string;
    lnPayment?: LnPayment;
  };

export const updateCoverLetter: UpdateCoverLetter<UpdateCoverLetterPayload, string> = async (
  { id, description, content, isCompleteCoverLetter, temperature, gptModel, lnPayment },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  await checkIfUserPaid({ context, lnPayment });

  const job = await context.entities.Job.findFirst({
    where: {
      id,
      user: { id: context.user.id },
    },
  });

  if (!job) {
    throw new HttpError(404, 'Job not found');
  }

  const coverLetter = await generateCoverLetter(
    {
      jobId: id,
      title: job.title,
      content,
      description: job.description,
      isCompleteCoverLetter,
      temperature,
      gptModel,
      lnPayment,
    },
    context
  );

  await context.entities.Job.update({
    where: {
      id,
    },
    data: {
      description,
      coverLetter: { connect: { id: coverLetter.id } },
    },
  });

  return coverLetter.id;
};

export const editCoverLetter: EditCoverLetter<{ coverLetterId: string; content: string }, CoverLetter> = (
  { coverLetterId, content },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.CoverLetter.update({
    where: {
      id: coverLetterId,
    },
    data: {
      content,
    },
  });
};

export const deleteJob: DeleteJob<{ jobId: string }, { count: number }> = ({ jobId }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  if (!jobId) {
    throw new HttpError(401);
  }

  return context.entities.Job.deleteMany({
    where: {
      id: jobId,
      userId: context.user.id,
    },
  });
};

type UpdateUserArgs = Partial<Pick<User, 'id' | 'notifyPaymentExpires' | 'gptModel' | 'username' | 'bio' | 'profileImageUrl' | 'darkMode' | 'wordCountDisplay' | 'autoSave' | 'jobReminders' | 'featureUpdates' | 'subscriptionReminders' | 'marketingEmails' | 'yearsOfExperience'>>;
type UserWithoutPassword = Omit<User, 'password'>;

export const updateUser: UpdateUser<UpdateUserArgs, UserWithoutPassword> = async (
  {
    notifyPaymentExpires,
    gptModel,
    username,
    bio,
    profileImageUrl,
    darkMode,
    wordCountDisplay,
    autoSave,
    jobReminders,
    featureUpdates,
    subscriptionReminders,
    marketingEmails,
    yearsOfExperience
  },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  // Check if username is being updated and if it's already taken
  if (username && username !== context.user.username) {
    const existingUser = await context.entities.User.findUnique({
      where: { username },
    });
    if (existingUser && existingUser.id !== context.user.id) {
      throw new HttpError(400, 'Username is already taken');
    }
  }

  return context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      notifyPaymentExpires,
      gptModel,
      username,
      bio,
      profileImageUrl,
      darkMode,
      wordCountDisplay,
      autoSave,
      jobReminders,
      featureUpdates,
      subscriptionReminders,
      marketingEmails,
      yearsOfExperience,
      updatedAt: new Date(),
    },
    select: {
      id: true,
      email: true,
      username: true,
      hasPaid: true,
      datePaid: true,
      notifyPaymentExpires: true,
      checkoutSessionId: true,
      stripeId: true,
      credits: true,
      gptModel: true,
      isUsingLn: true,
      subscriptionStatus: true,
      yearsOfExperience: true,
      profileImageUrl: true,
      bio: true,
      darkMode: true,
      wordCountDisplay: true,
      autoSave: true,
      jobReminders: true,
      featureUpdates: true,
      subscriptionReminders: true,
      marketingEmails: true,
      createdAt: true,
      updatedAt: true,
      lastLoginAt: true,
    },
  });
};

// Delete user account action
export const deleteUserAccount = async (_args: void, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    // Delete user and all related data (cascade deletes will handle most relations)
    await context.entities.User.delete({
      where: {
        id: context.user.id,
      },
    });

    return { success: true, message: 'Account deleted successfully' };
  } catch (error) {
    console.error('Error deleting user account:', error);
    throw new HttpError(500, 'Failed to delete account');
  }
};

type UpdateUserResult = Pick<User, 'id' | 'email' | 'hasPaid'>;

function dontUpdateUser(user: UserWithoutPassword): Promise<UserWithoutPassword> {
  return new Promise((resolve) => {
    resolve(user);
  });
}

type StripePaymentResult = {
  sessionUrl: string | null;
  sessionId: string;
};

export const stripePayment: StripePayment<void, StripePaymentResult> = async (_args, context) => {
  if (!context.user || !context.user.email) {
    throw new HttpError(401, 'User or email not found');
  }
  let customer: Stripe.Customer;
  const stripeCustomers = await stripe.customers.list({
    email: context.user.email,
  });
  if (!stripeCustomers.data.length) {
    console.log('creating customer');
    customer = await stripe.customers.create({
      email: context.user.email,
    });
  } else {
    console.log('using existing customer');
    customer = stripeCustomers.data[0];
  }

  const session: Stripe.Checkout.Session = await stripe.checkout.sessions.create({
    line_items: [
      {
        price: process.env.PRODUCT_PRICE_ID!,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${DOMAIN}/checkout?success=true`,
    cancel_url: `${DOMAIN}/checkout?canceled=true`,
    automatic_tax: { enabled: true },
    customer_update: {
      address: 'auto',
    },
    customer: customer.id,
  });

  await context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      checkoutSessionId: session?.id ?? null,
      stripeId: customer.id ?? null,
    },
  });

  return new Promise((resolve, reject) => {
    if (!session) {
      reject(new HttpError(402, 'Could not create a Stripe session'));
    } else {
      resolve({
        sessionUrl: session.url,
        sessionId: session.id,
      });
    }
  });
};

export const stripeGpt4Payment: StripeGpt4Payment<void, StripePaymentResult> = async (_args, context) => {
  if (!context.user || !context.user.email) {
    throw new HttpError(401, 'User or email not found');
  }
  let customer: Stripe.Customer;
  const stripeCustomers = await stripe.customers.list({
    email: context.user.email,
  });
  if (!stripeCustomers.data.length) {
    console.log('creating customer');
    customer = await stripe.customers.create({
      email: context.user.email,
    });
  } else {
    console.log('using existing customer');
    customer = stripeCustomers.data[0];
  }

  const session: Stripe.Checkout.Session = await stripe.checkout.sessions.create({
    line_items: [
      {
        price: process.env.GPT4_PRICE_ID!,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${DOMAIN}/checkout?success=true`,
    cancel_url: `${DOMAIN}/checkout?canceled=true`,
    automatic_tax: { enabled: true },
    customer_update: {
      address: 'auto',
    },
    customer: customer.id,
  });

  await context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      checkoutSessionId: session?.id ?? null,
      stripeId: customer.id ?? null,
    },
  });

  return new Promise((resolve, reject) => {
    if (!session) {
      reject(new HttpError(402, 'Could not create a Stripe session'));
    } else {
      resolve({
        sessionUrl: session.url,
        sessionId: session.id,
      });
    }
  });
};

export const stripeCreditsPayment: StripeCreditsPayment<void, StripePaymentResult> = async (_args, context) => {
  if (!context.user || !context.user.email) {
    throw new HttpError(401, 'User or email not found');
  }
  let customer: Stripe.Customer;
  const stripeCustomers = await stripe.customers.list({
    email: context.user.email,
  });
  if (!stripeCustomers.data.length) {
    console.log('creating customer');
    customer = await stripe.customers.create({
      email: context.user.email,
    });
  } else {
    console.log('using existing customer');
    customer = stripeCustomers.data[0];
  }

  const session: Stripe.Checkout.Session = await stripe.checkout.sessions.create({
    line_items: [
      {
        price: process.env.PRODUCT_CREDITS_PRICE_ID!,
        quantity: 1,
      },
    ],
    mode: 'payment',
    success_url: `${DOMAIN}/checkout?credits=true`,
    cancel_url: `${DOMAIN}/checkout?canceled=true`,
    automatic_tax: { enabled: true },
    customer_update: {
      address: 'auto',
    },
    customer: customer.id,
  });

  await context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      stripeId: customer.id ?? null,
    },
  });

  return new Promise((resolve, reject) => {
    if (!session) {
      reject(new HttpError(402, 'Could not create a Stripe session'));
    } else {
      resolve({
        sessionUrl: session.url,
        sessionId: session.id,
      });
    }
  });
};

// Learning Progress Actions
type UpdateLearningProgressArgs = {
  resourceId: string;
  resourceType: string;
  progress: number;
  completed?: boolean;
  timeSpent?: number;
};

export const updateLearningProgress = async (args: UpdateLearningProgressArgs, context: any) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { resourceId, resourceType, progress, completed = false, timeSpent = 0 } = args;

  const learningProgress = await context.entities.LearningProgress.upsert({
    where: {
      userId_resourceId: {
        userId: context.user.id,
        resourceId: resourceId,
      },
    },
    update: {
      progress: Math.min(100, Math.max(0, progress)),
      completed: completed || progress >= 100,
      timeSpent: timeSpent,
      lastAccessed: new Date(),
    },
    create: {
      userId: context.user.id,
      resourceId: resourceId,
      resourceType: resourceType,
      progress: Math.min(100, Math.max(0, progress)),
      completed: completed || progress >= 100,
      timeSpent: timeSpent,
    },
  });

  // Check for badge eligibility after updating progress
  await checkAndAwardBadges(context.user.id, context);

  return learningProgress;
};

// Initialize badges in the system
export const initializeBadges = async (_args: void, context: any) => {
  const badgeCriteria = [
    {
      name: "First Steps",
      description: "Complete your first learning video",
      icon: "🎯",
      category: "learning",
      criteria: { completedVideos: 1 },
      color: "green"
    },
    {
      name: "Learning Enthusiast",
      description: "Complete 5 learning videos",
      icon: "📚",
      category: "learning",
      criteria: { completedVideos: 5 },
      color: "blue"
    },
    {
      name: "Knowledge Seeker",
      description: "Complete 10 learning videos",
      icon: "🎓",
      category: "learning",
      criteria: { completedVideos: 10 },
      color: "purple"
    },
    {
      name: "Time Investor",
      description: "Spend 1 hour learning",
      icon: "⏰",
      category: "learning",
      criteria: { timeSpent: 3600 },
      color: "orange"
    },
    {
      name: "Dedicated Learner",
      description: "Spend 5 hours learning",
      icon: "📖",
      category: "learning",
      criteria: { timeSpent: 18000 },
      color: "red"
    },
    {
      name: "Career Starter",
      description: "Complete your first career development video",
      icon: "🚀",
      category: "achievement",
      criteria: { completedVideos: 1 },
      color: "teal"
    }
  ];

  const createdBadges = [];
  for (const badgeData of badgeCriteria) {
    const existingBadge = await context.entities.Badge.findUnique({
      where: { name: badgeData.name }
    });

    if (!existingBadge) {
      const badge = await context.entities.Badge.create({
        data: badgeData
      });
      createdBadges.push(badge);
    }
  }

  return { message: `Initialized ${createdBadges.length} new badges`, badges: createdBadges };
};

// Badge System Functions
const checkAndAwardBadges = async (userId: number, context: any) => {
  // Get user's learning progress
  const userProgress = await context.entities.LearningProgress.findMany({
    where: { userId: userId },
  });

  const completedCount = userProgress.filter(p => p.completed).length;
  const totalTimeSpent = userProgress.reduce((sum, p) => sum + p.timeSpent, 0);

  // Get all available badges
  const allBadges = await context.entities.Badge.findMany();

  // Check each badge criteria
  for (const badge of allBadges) {
    const criteria = badge.criteria as any;
    const meetsRequirement =
      (criteria.completedVideos && completedCount >= criteria.completedVideos) ||
      (criteria.timeSpent && totalTimeSpent >= criteria.timeSpent);

    if (meetsRequirement) {
      // Award badge to user if they don't have it
      const existingUserBadge = await context.entities.UserBadge.findUnique({
        where: {
          userId_badgeId: {
            userId: userId,
            badgeId: badge.id,
          }
        }
      });

      if (!existingUserBadge) {
        await context.entities.UserBadge.create({
          data: {
            userId: userId,
            badgeId: badge.id,
          }
        });
      }
    }
  }
};

// Performance Monitoring Action
type PerformanceMetric = {
  name: string;
  value: number;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  rating?: 'good' | 'needs-improvement' | 'poor';
  metadata?: any;
};

type ReportPerformanceMetricsArgs = {
  metrics: PerformanceMetric[];
};

export const reportPerformanceMetrics = async (args: ReportPerformanceMetricsArgs, context: any) => {
  const { metrics } = args;

  if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
    return { success: true, message: 'No metrics to report' };
  }

  try {
    // Log metrics for now (in production, you'd send to monitoring service)
    console.log('Performance Metrics Received:', {
      count: metrics.length,
      userId: context.user?.id,
      timestamp: new Date().toISOString(),
    });

    // Process each metric
    for (const metric of metrics) {
      // Validate metric structure
      if (!metric.name || typeof metric.value !== 'number') {
        continue;
      }

      // Log important metrics
      if (metric.name.includes('ERROR') || metric.rating === 'poor') {
        console.warn('Performance Issue Detected:', {
          metric: metric.name,
          value: metric.value,
          rating: metric.rating,
          url: metric.url,
          userId: context.user?.id,
          timestamp: new Date(metric.timestamp).toISOString(),
        });
      }

      // In production, you would:
      // 1. Store metrics in a time-series database (e.g., InfluxDB, TimescaleDB)
      // 2. Send to monitoring services (e.g., DataDog, New Relic, Sentry)
      // 3. Trigger alerts for critical performance issues
      // 4. Aggregate metrics for dashboards

      // Example: Store in database (you'd need to create a PerformanceMetric entity)
      // await context.entities.PerformanceMetric.create({
      //   data: {
      //     name: metric.name,
      //     value: metric.value,
      //     timestamp: new Date(metric.timestamp),
      //     url: metric.url,
      //     userAgent: metric.userAgent,
      //     userId: context.user?.id,
      //     rating: metric.rating,
      //     metadata: metric.metadata,
      //   },
      // });
    }

    return {
      success: true,
      message: `Successfully processed ${metrics.length} performance metrics`
    };
  } catch (error) {
    console.error('Error processing performance metrics:', error);
    // Don't throw error to avoid disrupting user experience
    return {
      success: false,
      message: 'Failed to process performance metrics'
    };
  }
};