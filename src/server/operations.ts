import { HttpError } from 'wasp/server';
import { type Resume } from '../shared/types';
import { type JobApplication } from 'wasp/entities';

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

export const createResume = async (args: Partial<Resume>, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const resume = await context.entities.Resume.create({
    data: {
      ...args,
      userId: context.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      personalInfo: args.personalInfo || {},
      experience: args.experience || [],
      education: args.education || [],
      skills: args.skills || [],
      certifications: args.certifications || [],
    },
  });

  return resume;
};

export const updateResume = async (args: { id: string; data: Partial<Resume> }, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const resume = await context.entities.Resume.findUnique({
    where: { id: args.id },
  });

  if (!resume || resume.userId !== context.user.id) {
    throw new HttpError(404);
  }

  const updatedResume = await context.entities.Resume.update({
    where: { id: args.id },
    data: {
      ...args.data,
      updatedAt: new Date(),
      personalInfo: args.data.personalInfo || resume.personalInfo,
      experience: args.data.experience || resume.experience,
      education: args.data.education || resume.education,
      skills: args.data.skills || resume.skills,
      certifications: args.data.certifications || resume.certifications,
      templateId: args.data.templateId !== undefined ? args.data.templateId : resume.templateId,
    },
  });

  return updatedResume;
};

export const deleteResume = async (args: { id: string }, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const resume = await context.entities.Resume.findUnique({
    where: { id: args.id },
  });

  if (!resume || resume.userId !== context.user.id) {
    throw new HttpError(404);
  }

  await context.entities.Resume.delete({
    where: { id: args.id },
  });

  return { success: true };
};

export const getUserResumes = async (_args: {}, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    const resumes = await context.entities.Resume.findMany({
      where: { userId: context.user.id },
      orderBy: { updatedAt: 'desc' },
    });

    return resumes;
  } catch (error) {
    console.error('Error fetching resumes:', error);
    throw new HttpError(500, 'Failed to fetch resumes');
  }
};

// Define the structure for interview questions
type InterviewQuestion = {
  category: string;
  question: string;
  tips: string;
  answer?: string;
  references?: string;
};

// Define the return type for the generateInterviewQuestions function
type GeneratedQuestionSet = {
  id?: string;
  jobId: string;
  jobTitle: string;
  company: string;
  jobDescription: string; // Add job description
  questions: { category: string; question: string; tips: string; answer?: string; references?: string }[];
  generatedAt: Date;
};

export const generateInterviewQuestions = async (args: { jobId: string }, context: any): Promise<GeneratedQuestionSet> => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    // Check if user has credits or has paid
    if (!context.user.hasPaid && !context.user.credits && !context.user.isUsingLn) {
      throw new HttpError(402, 'User has not paid or is out of credits');
    }

    // Get the job description and verify it belongs to the user
    const job = await context.entities.Job.findUnique({
      where: {
        id: args.jobId,
        userId: context.user.id // Ensure user owns this job
      },
    });

    if (!job) {
      throw new HttpError(404, 'Job not found or access denied');
    }

    // Use OpenAI to generate interview questions based on the job description
    const OpenAI = await import('openai');
    const openai = new OpenAI.default({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `
      Generate 10 interview questions based on the following job description.
      Include a mix of technical, behavioral, and situational questions that are specific to this role.
      For each question, provide:
      1. A brief tip on how to answer it effectively
      2. A sample answer that demonstrates a strong response to the question
      3. 1-2 references or sources that support the answer (books, articles, or websites)

      IMPORTANT: Your response MUST be a valid JSON object with a "questions" property containing an array of question objects:
      {
        "questions": [
          {
            "category": "Technical|Behavioral|Situational",
            "question": "The interview question",
            "tips": "Brief advice on how to answer this question effectively",
            "answer": "A sample strong answer to this question that demonstrates best practices",
            "references": "1-2 references or sources that support this answer (e.g., books, articles, websites)"
          }
        ]
      }

      DO NOT include any text before or after the JSON object. The response should be a valid JSON object.
      DO NOT use markdown formatting, code blocks, or any other non-JSON content.

      Job Title: ${job.title}
      Company: ${job.company}
      Job Description: ${job.description}
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo-0125", // Use a model that supports response_format
      messages: [
        {
          role: "system",
          content: "You are an expert career coach specializing in interview preparation. You always respond with valid JSON when asked to do so. You never include explanatory text, markdown formatting, or code blocks in your JSON responses."
        },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 3000,
      response_format: { type: "json_object" }
    });

    // Parse the response
    const content = response.choices[0].message.content || '';
    let questions: InterviewQuestion[] = [];

    try {
      // Log the raw content for debugging
      console.log('Raw AI response:', content);

      // Parse the JSON response
      try {
        // Since we're using response_format: { type: "json_object" }, we should get a valid JSON object
        const parsedResponse = JSON.parse(content);

        // Check if the response has a questions property
        if (parsedResponse.questions && Array.isArray(parsedResponse.questions)) {
          questions = parsedResponse.questions;
        }
        // If not, check if the response itself is an array
        else if (Array.isArray(parsedResponse)) {
          questions = parsedResponse;
        }
        // If it's neither, try to find any array property in the response
        else {
          const arrayProps = Object.keys(parsedResponse).filter(key =>
            Array.isArray(parsedResponse[key]) && parsedResponse[key].length > 0);

          if (arrayProps.length > 0) {
            questions = parsedResponse[arrayProps[0]];
          } else {
            throw new Error('No question array found in response');
          }
        }
      } catch (parseError) {
        console.error('Initial JSON parse error:', parseError);

        // Fallback: Try different approaches to extract JSON
        let jsonString = '';

        // First attempt: Look for JSON object pattern
        const jsonObjectMatch = content.match(/\{[\s\S]*\}/);
        if (jsonObjectMatch) {
          jsonString = jsonObjectMatch[0];
        }
        // Second attempt: Look for JSON array pattern
        else {
          const jsonArrayMatch = content.match(/\[[\s\S]*\]/);
          if (jsonArrayMatch) {
            jsonString = jsonArrayMatch[0];
          }
          // Third attempt: Look for JSON in code blocks (```json ... ```)
          else {
            const codeBlockMatch = content.match(/```(?:json)?\s*([\s\S]*?)```/);
            if (codeBlockMatch && codeBlockMatch[1]) {
              jsonString = codeBlockMatch[1].trim();
            }
          }
        }

        // If we found a JSON string, try to parse it
        if (jsonString) {
          try {
            const parsedJson = JSON.parse(jsonString);

            // Check if it's an array or has a questions property
            if (Array.isArray(parsedJson)) {
              questions = parsedJson;
            } else if (parsedJson.questions && Array.isArray(parsedJson.questions)) {
              questions = parsedJson.questions;
            } else {
              // Look for any array property
              const arrayProps = Object.keys(parsedJson).filter(key =>
                Array.isArray(parsedJson[key]) && parsedJson[key].length > 0);

              if (arrayProps.length > 0) {
                questions = parsedJson[arrayProps[0]];
              } else {
                throw new Error('No question array found in parsed JSON');
              }
            }
          } catch (secondParseError) {
            console.error('Second JSON parse error:', secondParseError);
            throw new Error('Failed to parse JSON from response');
          }
        } else {
          // Last resort: Try to manually extract structured data
          console.log('Attempting to manually extract structured data');

          // Simple pattern matching for questions
          const questionBlocks = content.split(/Question \d+:|Category:|Technical:|Behavioral:|Situational:/g)
            .filter(block => block.trim().length > 0);

          if (questionBlocks.length > 0) {
            questions = questionBlocks.map((block, index) => {
              const lines = block.split('\n').filter(line => line.trim().length > 0);
              return {
                category: block.includes('Technical') ? 'Technical' :
                          block.includes('Behavioral') ? 'Behavioral' : 'Situational',
                question: lines[0]?.trim() || `Question ${index + 1}`,
                tips: lines.find(l => l.includes('Tip:') || l.includes('Tips:'))?.replace(/(Tip:|Tips:)/g, '').trim() ||
                      'No tips provided',
                answer: lines.find(l => l.includes('Answer:') || l.includes('Sample Answer:'))?.replace(/(Answer:|Sample Answer:)/g, '').trim() ||
                        'No sample answer provided',
                references: lines.find(l => l.includes('Reference:') || l.includes('References:'))?.replace(/(Reference:|References:)/g, '').trim() ||
                            'No references provided'
              };
            });
          } else {
            throw new Error('Could not extract structured data from response');
          }
        }
      }

      // Validate the questions array
      if (!Array.isArray(questions) || questions.length === 0) {
        throw new Error('Failed to extract valid questions array');
      }

      // Ensure each question has the required fields
      questions = questions.map((q, index) => ({
        category: q.category || 'General',
        question: q.question || `Question ${index + 1}`,
        tips: q.tips || 'No tips provided',
        answer: q.answer || 'No sample answer provided',
        references: q.references || 'No references provided'
      }));

    } catch (error) {
      console.error('Error parsing questions:', error);
      console.error('Response content:', content);
      throw new HttpError(500, 'Failed to parse generated questions');
    }

    // Decrement credits if user hasn't paid (after successful generation)
    if (context.user.credits && !context.user.hasPaid && !context.user.isUsingLn) {
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            decrement: 1,
          },
        },
      });
    }

    // Check if we already have a question set for this job and user
    const existingQuestionSet = await context.entities.InterviewQuestionSet.findFirst({
      where: {
        userId: context.user.id,
        jobId: job.id
      }
    });

    if (existingQuestionSet) {
      // Merge with existing questions
      const existingQuestions = existingQuestionSet.questions as InterviewQuestion[];
      const mergedQuestions = [...existingQuestions, ...questions];

      // Update the existing question set
      const updatedQuestionSet = await context.entities.InterviewQuestionSet.update({
        where: { id: existingQuestionSet.id },
        data: {
          questions: mergedQuestions,
          generatedAt: new Date(),
          updatedAt: new Date()
        }
      });

      return {
        id: updatedQuestionSet.id,
        jobId: job.id,
        jobTitle: job.title,
        company: job.company,
        jobDescription: job.description,
        questions: questions.map(q => ({
          category: q.category,
          question: q.question,
          tips: q.tips,
          answer: q.answer,
          references: q.references
        })),
        generatedAt: updatedQuestionSet.generatedAt
      };
    } else {
      // Create new question set
      const questionSet = await context.entities.InterviewQuestionSet.create({
        data: {
          userId: context.user.id,
          jobId: job.id,
          jobTitle: job.title,
          company: job.company,
          jobDescription: job.description,
          questions: questions,
          generatedAt: new Date()
        }
      });

      return {
        id: questionSet.id,
        jobId: job.id,
        jobTitle: job.title,
        company: job.company,
        jobDescription: job.description,
        questions: questions.map(q => ({
          category: q.category,
          question: q.question,
          tips: q.tips,
          answer: q.answer,
          references: q.references
        })),
        generatedAt: questionSet.generatedAt
      };
    }
  } catch (error: any) {
    // Restore credits if there was an error after decrementing
    if (!context.user.hasPaid && error?.statusCode !== 402) {
      try {
        await context.entities.User.update({
          where: { id: context.user.id },
          data: {
            credits: {
              increment: 1,
            },
          },
        });
      } catch (restoreError) {
        console.error('Error restoring credits:', restoreError);
      }
    }
    console.error('Error generating interview questions:', error);
    throw new HttpError(error.statusCode || 500, error.message || 'Failed to generate interview questions');
  }
};

// Get all interview question sets for a user
export const getInterviewQuestionSets = async (_args: void, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    const questionSets = await context.entities.InterviewQuestionSet.findMany({
      where: { userId: context.user.id },
      orderBy: { generatedAt: 'desc' }
    });

    return questionSets.map(set => ({
      id: set.id,
      jobId: set.jobId,
      jobTitle: set.jobTitle,
      company: set.company,
      jobDescription: set.jobDescription,
      questions: set.questions,
      generatedAt: set.generatedAt
    }));
  } catch (error) {
    console.error('Error fetching interview question sets:', error);
    throw new HttpError(500, 'Failed to fetch interview question sets');
  }
};

// Save an interview question
export const saveInterviewQuestion = async (args: {
  questionType: string;
  questionId?: string;
  questionData: any;
  notes?: string;
}, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    const savedQuestion = await context.entities.SavedInterviewQuestion.upsert({
      where: {
        userId_questionType_questionId: {
          userId: context.user.id,
          questionType: args.questionType,
          questionId: args.questionId || ''
        }
      },
      update: {
        questionData: args.questionData,
        notes: args.notes
      },
      create: {
        userId: context.user.id,
        questionType: args.questionType,
        questionId: args.questionId,
        questionData: args.questionData,
        notes: args.notes
      }
    });

    return savedQuestion;
  } catch (error) {
    console.error('Error saving interview question:', error);
    throw new HttpError(500, 'Failed to save interview question');
  }
};

// Get saved interview questions for a user
export const getSavedInterviewQuestions = async (_args: void, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    const savedQuestions = await context.entities.SavedInterviewQuestion.findMany({
      where: { userId: context.user.id },
      orderBy: { savedAt: 'desc' }
    });

    return savedQuestions;
  } catch (error) {
    console.error('Error fetching saved interview questions:', error);
    throw new HttpError(500, 'Failed to fetch saved interview questions');
  }
};

// Remove a saved interview question
export const removeSavedInterviewQuestion = async (args: { id: string }, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    await context.entities.SavedInterviewQuestion.deleteMany({
      where: {
        id: args.id,
        userId: context.user.id
      }
    });

    return { success: true };
  } catch (error) {
    console.error('Error removing saved interview question:', error);
    throw new HttpError(500, 'Failed to remove saved interview question');
  }
};

// Save a practice answer
export const savePracticeAnswer = async (args: {
  questionType: string;
  questionData: any;
  userAnswer: string;
  notes?: string;
}, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    const practiceAnswer = await context.entities.PracticeAnswer.create({
      data: {
        userId: context.user.id,
        questionType: args.questionType,
        questionData: args.questionData,
        userAnswer: args.userAnswer,
        notes: args.notes
      }
    });

    return practiceAnswer;
  } catch (error) {
    console.error('Error saving practice answer:', error);
    throw new HttpError(500, 'Failed to save practice answer');
  }
};

// Get practice answers for a user
export const getPracticeAnswers = async (_args: void, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    const practiceAnswers = await context.entities.PracticeAnswer.findMany({
      where: { userId: context.user.id },
      orderBy: { savedAt: 'desc' }
    });

    return practiceAnswers;
  } catch (error) {
    console.error('Error fetching practice answers:', error);
    throw new HttpError(500, 'Failed to fetch practice answers');
  }
};

// Delete a practice answer
export const deletePracticeAnswer = async (args: { id: string }, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    await context.entities.PracticeAnswer.deleteMany({
      where: {
        id: args.id,
        userId: context.user.id
      }
    });

    return { success: true };
  } catch (error) {
    console.error('Error deleting practice answer:', error);
    throw new HttpError(500, 'Failed to delete practice answer');
  }
};

// JobApplication operations
type CreateJobApplicationArgs = {
  jobId: string;
  dateApplied: string;
  status: string;
  notes?: string;
  salary?: string;
  contactPerson?: string;
  contactEmail?: string;
  followUpDate?: string;
};

export const createJobApplication = async (args: CreateJobApplicationArgs, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    // Check if the job exists and belongs to the user
    const job = await context.entities.Job.findUnique({
      where: { id: args.jobId },
    });

    if (!job) {
      throw new HttpError(404, 'Job not found');
    }

    // Create the job application
    const jobApplication = await context.entities.JobApplication.create({
      data: {
        jobId: args.jobId,
        userId: context.user.id,
        dateApplied: new Date(args.dateApplied),
        status: args.status,
        notes: args.notes,
        salary: args.salary,
        contactPerson: args.contactPerson,
        contactEmail: args.contactEmail,
        followUpDate: args.followUpDate ? new Date(args.followUpDate) : null,
      },
    });

    return jobApplication;
  } catch (error) {
    console.error('Error creating job application:', error);
    throw new HttpError(500, 'Failed to create job application');
  }
};

type UpdateJobApplicationArgs = {
  id: string;
  dateApplied?: string;
  status?: string;
  notes?: string;
  salary?: string;
  contactPerson?: string;
  contactEmail?: string;
  followUpDate?: string;
};

export const updateJobApplication = async (args: UpdateJobApplicationArgs, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    // Check if the job application exists and belongs to the user
    const jobApplication = await context.entities.JobApplication.findUnique({
      where: { id: args.id },
    });

    if (!jobApplication || jobApplication.userId !== context.user.id) {
      throw new HttpError(404, 'Job application not found');
    }

    // Update the job application
    const updatedJobApplication = await context.entities.JobApplication.update({
      where: { id: args.id },
      data: {
        dateApplied: args.dateApplied ? new Date(args.dateApplied) : undefined,
        status: args.status,
        notes: args.notes,
        salary: args.salary,
        contactPerson: args.contactPerson,
        contactEmail: args.contactEmail,
        followUpDate: args.followUpDate ? new Date(args.followUpDate) : null,
      },
    });

    return updatedJobApplication;
  } catch (error) {
    console.error('Error updating job application:', error);
    throw new HttpError(500, 'Failed to update job application');
  }
};

export const deleteJobApplication = async (args: { id: string }, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    // Check if the job application exists and belongs to the user
    const jobApplication = await context.entities.JobApplication.findUnique({
      where: { id: args.id },
    });

    if (!jobApplication || jobApplication.userId !== context.user.id) {
      throw new HttpError(404, 'Job application not found');
    }

    // Delete the job application
    await context.entities.JobApplication.delete({
      where: { id: args.id },
    });

    return { success: true };
  } catch (error) {
    console.error('Error deleting job application:', error);
    throw new HttpError(500, 'Failed to delete job application');
  }
};

// Parse PDF content and extract resume data using AI
export const parsePDFContent = async (args: {
  pdfText: string;
  fileName: string;
}, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    // Check if user has credits or has paid
    if (!context.user.hasPaid && !context.user.credits && !context.user.isUsingLn) {
      throw new HttpError(402, 'User has not paid or is out of credits');
    }

    // Decrement credits if user hasn't paid
    if (context.user.credits && !context.user.hasPaid) {
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            decrement: 1,
          },
        },
      });
    }

    const prompt = `You are an expert resume parser. Extract and structure the following resume content into a JSON format.

    Please parse the following resume text and return a JSON object with this exact structure:
    {
      "personalInfo": {
        "fullName": "string",
        "email": "string",
        "phone": "string",
        "location": "string",
        "linkedIn": "string (optional)",
        "website": "string (optional)"
      },
      "summary": "string - professional summary or objective",
      "experience": [
        {
          "id": "string - generate unique id",
          "company": "string",
          "position": "string",
          "startDate": "string - format YYYY-MM",
          "endDate": "string - format YYYY-MM or 'Present'",
          "current": boolean,
          "description": "string - job description",
          "achievements": ["string array of key achievements"]
        }
      ],
      "education": [
        {
          "id": "string - generate unique id",
          "institution": "string",
          "degree": "string",
          "field": "string",
          "startDate": "string - format YYYY-MM",
          "endDate": "string - format YYYY-MM or 'Present'",
          "current": boolean,
          "gpa": "string (optional)"
        }
      ],
      "skills": ["string array of skills"],
      "certifications": [
        {
          "id": "string - generate unique id",
          "name": "string",
          "issuer": "string",
          "date": "string - format YYYY-MM"
        }
      ]
    }

    Extract as much information as possible. If a field is not found, use appropriate defaults or empty values.
    For dates, try to parse them into YYYY-MM format. If only year is available, use YYYY-01.
    Generate unique IDs for each experience, education, and certification entry.

    Resume text to parse:
    ${args.pdfText}`;

    const payload = {
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an expert resume parser. Always respond with valid JSON when asked to do so. Never include explanatory text, markdown formatting, or code blocks in your JSON responses."
        },
        { role: "user", content: prompt }
      ],
      temperature: 0.1,
      max_tokens: 4000,
      response_format: { type: "json_object" }
    };

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
      },
      method: 'POST',
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new HttpError(response.status, `OpenAI API error: ${response.statusText}`);
    }

    const json = await response.json() as OpenAIResponse;

    if (!json.choices || !json.choices[0] || !json.choices[0].message) {
      throw new HttpError(500, 'Invalid response format from OpenAI');
    }

    const content = json.choices[0].message.content;

    try {
      const parsedData = JSON.parse(content);

      // Validate the parsed data structure
      if (!parsedData.personalInfo || !parsedData.experience || !parsedData.education || !parsedData.skills) {
        throw new Error('Invalid resume data structure');
      }

      return {
        title: args.fileName.replace(/\.[^/.]+$/, ""), // Remove file extension
        ...parsedData
      };

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.error('AI response content:', content);
      throw new HttpError(500, 'Failed to parse resume data from AI response');
    }

  } catch (error) {
    console.error('Error parsing PDF content:', error);
    throw new HttpError(500, 'Failed to parse PDF content');
  }
};