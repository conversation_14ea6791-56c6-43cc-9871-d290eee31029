import { type CoverLetter, type Job, type JobApplication, type User, type LearningProgress, type UserBadge, type Badge } from "wasp/entities";
import { HttpError } from "wasp/server";
import {
  type GetCoverLetter,
  type GetJobs,
  type GetJob,
  type GetUserInfo,
  type GetCoverLetterCount,
  type GetAllCoverLetters,
  type GetJobApplications,
} from "wasp/server/operations";

export const getCoverLetter: GetCoverLetter<Pick<CoverLetter, 'id'> , CoverLetter> = async ({ id }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.CoverLetter.findFirstOrThrow({
    where: {
      id,
      user: { id: context.user.id },
    },
  });
};

type GetCoverLetterArgs = {
  id: string;
};

export const getCoverLetters: GetCoverLetter<GetCoverLetterArgs, CoverLetter[]> = async ({ id }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.CoverLetter.findMany({
    where: {
      job: { id },
      user: { id: context.user.id },
    },
  });
};

export const getAllCoverLetters: GetAllCoverLetters<void, CoverLetter[]> = async (_, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.CoverLetter.findMany({
    where: {
      user: { id: context.user.id },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
};

export const getJobs: GetJobs<void, Job[]> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.Job.findMany({
    where: {
      user: { id: context.user.id },
    },
    include: {
      coverLetter: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
};

type GetJobArgs = { id: string };
type GetJobResult = (Job & { coverLetter: CoverLetter[] });

export const getJob: GetJob<GetJobArgs, GetJobResult> = async ({ id }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.Job.findFirstOrThrow({
    where: {
      id,
      user: { id: context.user.id },
    },
    include: {
      coverLetter: true,
    },
  });
};

export const getUserInfo = async (_args: any, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.User.findUniqueOrThrow({
    where: {
      id: context.user.id,
    },
    select: {
      letters: true,
      id: true,
      email: true,
      username: true,
      hasPaid: true,
      notifyPaymentExpires: true,
      credits: true,
      gptModel: true,
      isUsingLn: true,
      subscriptionStatus: true,
      yearsOfExperience: true,
      // Profile settings
      profileImageUrl: true,
      bio: true,
      // User preferences
      darkMode: true,
      wordCountDisplay: true,
      autoSave: true,
      // Notification preferences
      jobReminders: true,
      featureUpdates: true,
      subscriptionReminders: true,
      marketingEmails: true,
      // Account settings
      lastLoginAt: true,
      createdAt: true,
      updatedAt: true,
    },
  });
};

// Get user preferences and profile data
export const getUserPreferences = async (_args: any, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.User.findUniqueOrThrow({
    where: {
      id: context.user.id,
    },
    select: {
      id: true,
      username: true,
      email: true,
      yearsOfExperience: true,
      bio: true,
      profileImageUrl: true,
      darkMode: true,
      wordCountDisplay: true,
      autoSave: true,
      jobReminders: true,
      featureUpdates: true,
      subscriptionReminders: true,
      marketingEmails: true,
      createdAt: true,
      updatedAt: true,
      lastLoginAt: true,
    },
  });
};


export const getCoverLetterCount: GetCoverLetterCount<void, number> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.CoverLetter.count({
    where: {
      userId: context.user.id
    }
  });
}

export const getJobApplications: GetJobApplications<void, any> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.JobApplication.findMany({
    where: {
      userId: context.user.id
    },
    include: {
      job: true
    },
    orderBy: {
      dateApplied: 'desc'
    }
  });
}

// Learning Progress Queries
export const getLearningProgress = async (_args: void, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.LearningProgress.findMany({
    where: {
      userId: context.user.id
    },
    orderBy: {
      lastAccessed: 'desc'
    }
  });
};

export const getUserBadges = async (_args: void, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.UserBadge.findMany({
    where: {
      userId: context.user.id
    },
    include: {
      badge: true
    },
    orderBy: {
      earnedAt: 'desc'
    }
  });
};

export const getAllBadges = async (_args: void, context: any) => {
  return context.entities.Badge.findMany({
    orderBy: {
      name: 'asc'
    }
  });
};