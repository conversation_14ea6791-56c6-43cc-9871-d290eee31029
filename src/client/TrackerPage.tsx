import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
  Badge,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Flex,
  Icon,
  Divider,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Select,
  Textarea,
  useDisclosure,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Tag,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  StatGroup,
  Grid,
  GridItem,
  Spinner
} from '@chakra-ui/react';
import {
  FaSearch,
  FaPlus,
  FaBuilding,
  FaBriefcase,
  FaCalendarAlt,
  FaChevronDown,
  FaFilter,
  FaChartLine,
  FaCheckCircle,
  FaTimesCircle,
  FaHourglassHalf,
  FaExclamationCircle,
  FaEllipsisV,
  FaEdit,
  FaTrash,
  FaSort,
  FaSortUp,
  FaSortDown
} from 'react-icons/fa';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ContentContainer from './components/ContentContainer';
import ActionButton from './components/ActionButton';
import { useQuery, useAction } from 'wasp/client/operations';
import { EmptyApplications, EmptyJobs } from './components/EmptyState';
import { SimpleLoading } from './components/LoadingState';
import { GeneralError } from './components/ErrorState';
import { getJobApplications, getJobs } from 'wasp/client/operations';
import { createJobApplication, updateJobApplication, deleteJobApplication } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';

// Define the JobApplication type
type JobApplication = {
  id: string;
  jobId: string;
  job: {
    id: string;
    title: string;
    company: string;
    location: string;
  };
  dateApplied: string;
  status: string;
  notes?: string;
  salary?: string;
  contactPerson?: string;
  contactEmail?: string;
  followUpDate?: string;
};

// Status options for job applications
const statusOptions = ["Saved", "Applied", "Interview", "Offer", "Rejected"];

// Status color mapping
const statusColorMap: Record<string, string> = {
  "Saved": "gray",
  "Applied": "blue",
  "Interview": "orange",
  "Offer": "green",
  "Rejected": "red"
};

// Status icon mapping
const statusIconMap: Record<string, any> = {
  "Saved": FaHourglassHalf,
  "Applied": FaCheckCircle,
  "Interview": FaCalendarAlt,
  "Offer": FaCheckCircle,
  "Rejected": FaTimesCircle
};

export default function TrackerPage() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortField, setSortField] = useState<string | null>("dateApplied");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [formData, setFormData] = useState({
    jobId: '',
    dateApplied: new Date().toISOString().split('T')[0],
    status: 'Applied',
    notes: '',
    salary: '',
    contactPerson: '',
    contactEmail: '',
    followUpDate: ''
  });

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const { data: user } = useAuth();

  // Fetch job applications only if user is authenticated
  const { data: applications, isLoading: isLoadingApplications, error: applicationsError } =
    useQuery(getJobApplications, undefined, { enabled: !!user });

  // Fetch jobs for the dropdown only if user is authenticated
  const { data: jobs, isLoading: isLoadingJobs } = useQuery(getJobs, undefined, { enabled: !!user });

  // Actions
  const createApplication = useAction(createJobApplication);
  const updateApplication = useAction(updateJobApplication);
  const deleteApplication = useAction(deleteJobApplication);

  // Filter and sort job applications
  const filteredApplications = applications
    ? applications
        .filter((application: JobApplication) => {
          const matchesSearch =
            application.job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
            application.job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            application.job.location.toLowerCase().includes(searchQuery.toLowerCase());
          const matchesStatus = statusFilter ? application.status === statusFilter : true;
          return matchesSearch && matchesStatus;
        })
        .sort((a: JobApplication, b: JobApplication) => {
          if (!sortField) return 0;

          let aValue, bValue;

          if (sortField === 'company') {
            aValue = a.job.company;
            bValue = b.job.company;
          } else if (sortField === 'position') {
            aValue = a.job.title;
            bValue = b.job.title;
          } else if (sortField === 'dateApplied') {
            aValue = new Date(a.dateApplied).getTime();
            bValue = new Date(b.dateApplied).getTime();
          } else {
            aValue = (a as any)[sortField];
            bValue = (b as any)[sortField];
          }

          if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
          if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
          return 0;
        })
    : [];

  // Calculate statistics
  const stats = applications
    ? {
        total: applications.length,
        applied: applications.filter((app: JobApplication) => app.status === "Applied").length,
        interviews: applications.filter((app: JobApplication) => app.status === "Interview").length,
        offers: applications.filter((app: JobApplication) => app.status === "Offer").length,
        rejected: applications.filter((app: JobApplication) => app.status === "Rejected").length,
        saved: applications.filter((app: JobApplication) => app.status === "Saved").length,
        responseRate: Math.round((applications.filter((app: JobApplication) => app.status === "Interview" || app.status === "Offer").length /
                               (applications.length - applications.filter((app: JobApplication) => app.status === "Saved").length)) * 100) || 0
      }
    : {
        total: 0,
        applied: 0,
        interviews: 0,
        offers: 0,
        rejected: 0,
        saved: 0,
        responseRate: 0
      };

  // Handle sort toggle
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Render sort icon
  const renderSortIcon = (field: string) => {
    if (sortField !== field) return FaSort;
    return sortDirection === 'asc' ? FaSortUp : FaSortDown;
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await createApplication({
        jobId: formData.jobId,
        dateApplied: formData.dateApplied,
        status: formData.status,
        notes: formData.notes,
        salary: formData.salary,
        contactPerson: formData.contactPerson,
        contactEmail: formData.contactEmail,
        followUpDate: formData.followUpDate || undefined
      });

      // Reset form and close modal
      setFormData({
        jobId: '',
        dateApplied: new Date().toISOString().split('T')[0],
        status: 'Applied',
        notes: '',
        salary: '',
        contactPerson: '',
        contactEmail: '',
        followUpDate: ''
      });
      onClose();
    } catch (error) {
      console.error('Error creating job application:', error);
    }
  };

  // Handle application deletion
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this application?')) {
      try {
        await deleteApplication({ id });
      } catch (error) {
        console.error('Error deleting job application:', error);
      }
    }
  };

  return (
    <ContentPageBox>
      <PageHeader
        title="Job Application Tracker"
        subtitle="Track and manage your job applications in one place"
      >
        <ActionButton
          icon={FaPlus}
          label="Add Application"
          variant="primary"
          onClick={onOpen}
        />
      </PageHeader>

      <ContentContainer delay={0.3}>
        <Grid templateColumns={{ base: "1fr", md: "repeat(4, 1fr)" }} gap={4} mb={6}>
          <GridItem colSpan={{ base: 1, md: 4 }}>
            <StatGroup bg={cardBg} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Stat>
                <StatLabel>Total Applications</StatLabel>
                <StatNumber>{stats.total}</StatNumber>
              </Stat>
              <Stat>
                <StatLabel>Interviews</StatLabel>
                <StatNumber>{stats.interviews}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  {stats.responseRate}% Response Rate
                </StatHelpText>
              </Stat>
              <Stat>
                <StatLabel>Offers</StatLabel>
                <StatNumber>{stats.offers}</StatNumber>
              </Stat>
              <Stat>
                <StatLabel>Active Applications</StatLabel>
                <StatNumber>{stats.applied + stats.interviews}</StatNumber>
              </Stat>
            </StatGroup>
          </GridItem>
        </Grid>

        <HStack spacing={4} mb={6}>
          <InputGroup flex={1}>
            <InputLeftElement pointerEvents="none">
              <Icon as={FaSearch} color="gray.400" />
            </InputLeftElement>
            <Input
              placeholder="Search by company, position, or location..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              borderRadius="md"
            />
          </InputGroup>

          <Menu>
            <MenuButton as={Button} rightIcon={<FaChevronDown />} leftIcon={<FaFilter />} variant="outline">
              {statusFilter || "All Statuses"}
            </MenuButton>
            <MenuList>
              <MenuItem onClick={() => setStatusFilter(null)}>All Statuses</MenuItem>
              <Divider />
              {statusOptions.map(status => (
                <MenuItem key={status} onClick={() => setStatusFilter(status)}>
                  <HStack>
                    <Icon as={statusIconMap[status]} color={`${statusColorMap[status]}.500`} />
                    <Text>{status}</Text>
                  </HStack>
                </MenuItem>
              ))}
            </MenuList>
          </Menu>
        </HStack>

        <Box overflowX="auto">
          {isLoadingApplications ? (
            <SimpleLoading message="Loading your job applications..." />
          ) : applicationsError ? (
            <GeneralError
              error={applicationsError}
              onRetry={() => window.location.reload()}
              showErrorDetails={false}
            />
          ) : (
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th cursor="pointer" onClick={() => handleSort('company')}>
                    <HStack spacing={1}>
                      <Text>Company</Text>
                      <Icon as={typeof renderSortIcon('company') === 'function' ? FaSort : renderSortIcon('company')} boxSize={3} />
                    </HStack>
                  </Th>
                  <Th cursor="pointer" onClick={() => handleSort('position')}>
                    <HStack spacing={1}>
                      <Text>Position</Text>
                      <Icon as={typeof renderSortIcon('position') === 'function' ? FaSort : renderSortIcon('position')} boxSize={3} />
                    </HStack>
                  </Th>
                  <Th cursor="pointer" onClick={() => handleSort('dateApplied')}>
                    <HStack spacing={1}>
                      <Text>Date Applied</Text>
                      <Icon as={typeof renderSortIcon('dateApplied') === 'function' ? FaSort : renderSortIcon('dateApplied')} boxSize={3} />
                    </HStack>
                  </Th>
                  <Th>Status</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredApplications.map((application: JobApplication) => (
                  <Tr key={application.id}>
                    <Td>
                      <HStack>
                        <Icon as={FaBuilding} color="gray.500" />
                        <Text fontWeight="medium">{application.job.company}</Text>
                      </HStack>
                    </Td>
                    <Td>
                      <VStack align="start" spacing={1}>
                        <Text>{application.job.title}</Text>
                        <Text fontSize="xs" color="gray.500">{application.job.location}</Text>
                      </VStack>
                    </Td>
                    <Td>{new Date(application.dateApplied).toLocaleDateString()}</Td>
                    <Td>
                      <Badge colorScheme={statusColorMap[application.status]}>
                        {application.status}
                      </Badge>
                    </Td>
                    <Td>
                      <Menu>
                        <MenuButton as={Button} size="sm" variant="ghost">
                          <Icon as={FaEllipsisV} />
                        </MenuButton>
                        <MenuList>
                          <MenuItem>
                            <HStack>
                              <Icon as={FaEdit} />
                              <Text>Edit</Text>
                            </HStack>
                          </MenuItem>
                          <MenuItem onClick={() => handleDelete(application.id)}>
                            <HStack>
                              <Icon as={FaTrash} color="red.500" />
                              <Text color="red.500">Delete</Text>
                            </HStack>
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          )}

          {!isLoadingApplications && !applicationsError && filteredApplications.length === 0 && (
            applications && applications.length > 0 ? (
              <Text textAlign="center" py={10} color="gray.500">
                No job applications match your search criteria.
              </Text>
            ) : (
              <EmptyApplications
                primaryAction={{
                  label: 'Add Application',
                  onClick: onOpen,
                  icon: <FaPlus />,
                  colorScheme: 'purple',
                }}
              />
            )
          )}
        </Box>
      </ContentContainer>

      {/* Add Job Application Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Add New Job Application</ModalHeader>
          <ModalCloseButton />
          <form onSubmit={handleSubmit}>
            <ModalBody>
              <VStack spacing={4}>
                <FormControl isRequired>
                  <FormLabel>Select Job</FormLabel>
                  {isLoadingJobs ? (
                    <SimpleLoading message="Loading your jobs..." />
                  ) : !jobs || jobs.length === 0 ? (
                    <EmptyJobs
                      size="sm"
                      title="No Jobs Available"
                      description="You need to add some jobs first before tracking applications."
                      showActions={false}
                    />
                  ) : (
                    <Select
                      name="jobId"
                      value={formData.jobId}
                      onChange={handleInputChange}
                      placeholder="Select a job"
                      isRequired
                    >
                      {jobs.map((job: any) => (
                        <option key={job.id} value={job.id}>
                          {job.title} at {job.company}
                        </option>
                      ))}
                    </Select>
                  )}
                </FormControl>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} width="100%">
                  <FormControl isRequired>
                    <FormLabel>Date Applied</FormLabel>
                    <Input
                      type="date"
                      name="dateApplied"
                      value={formData.dateApplied}
                      onChange={handleInputChange}
                      isRequired
                    />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>Status</FormLabel>
                    <Select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      isRequired
                    >
                      {statusOptions.map(status => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </Select>
                  </FormControl>
                </SimpleGrid>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} width="100%">
                  <FormControl>
                    <FormLabel>Salary Range</FormLabel>
                    <Input
                      placeholder="e.g. $80,000 - $100,000"
                      name="salary"
                      value={formData.salary}
                      onChange={handleInputChange}
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel>Follow-up Date</FormLabel>
                    <Input
                      type="date"
                      name="followUpDate"
                      value={formData.followUpDate}
                      onChange={handleInputChange}
                    />
                  </FormControl>
                </SimpleGrid>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} width="100%">
                  <FormControl>
                    <FormLabel>Contact Person</FormLabel>
                    <Input
                      placeholder="Name of recruiter or hiring manager"
                      name="contactPerson"
                      value={formData.contactPerson}
                      onChange={handleInputChange}
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel>Contact Email</FormLabel>
                    <Input
                      type="email"
                      placeholder="Email address"
                      name="contactEmail"
                      value={formData.contactEmail}
                      onChange={handleInputChange}
                    />
                  </FormControl>
                </SimpleGrid>

                <FormControl>
                  <FormLabel>Notes</FormLabel>
                  <Textarea
                    placeholder="Add any notes about the application, interview process, etc."
                    rows={4}
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                  />
                </FormControl>
              </VStack>
            </ModalBody>

            <ModalFooter>
              <Button variant="outline" mr={3} onClick={onClose}>
                Cancel
              </Button>
              <Button
                colorScheme="purple"
                type="submit"
                isDisabled={!formData.jobId || !formData.dateApplied || !formData.status}
              >
                Save Application
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </ContentPageBox>
  );
}
