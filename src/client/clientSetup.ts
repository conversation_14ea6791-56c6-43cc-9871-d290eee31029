import { configureQueryClient } from "wasp/client/operations";

export default async function mySetupFunction() {
  configureQueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        refetchOnMount: false, // Prevent refetch on component mount
        staleTime: 10 * 60 * 1000, // Increased to 10 minutes
        cacheTime: 15 * 60 * 1000, // Increased to 15 minutes
        refetchInterval: false, // Disable automatic refetching
        refetchIntervalInBackground: false, // Disable background refetching
        retry: (failureCount, error: any) => {
          // Don't retry on 401 errors (authentication failures)
          if (error?.response?.status === 401) {
            return false;
          }
          // Only retry up to 2 times for other errors
          return failureCount < 2;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
    },
  });
}
