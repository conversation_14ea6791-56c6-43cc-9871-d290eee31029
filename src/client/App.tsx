import { useAuth } from 'wasp/client/auth';
import {
  <PERSON>kraProvider,
  VStack,
  Box,
  Flex,
  Text,
  Button,
  HStack
} from '@chakra-ui/react';
import { theme } from './theme';
import { useState, useEffect, createContext } from 'react';
import NavBar, { SidebarContext, SidebarContextType } from './components/NavBar';
import Footer from './components/Footer';
import { EditPopover } from './components/Popover';
import { useLocation, Outlet, useNavigate } from 'react-router-dom';
import SidebarNav from './components/SidebarNav';
import AnimatedHeroText from './components/AnimatedHeroText';
import { FaFileAlt, FaEnvelopeOpenText, FaBriefcase, FaComments, FaChartLine } from 'react-icons/fa';
import ErrorBoundary from './components/ErrorBoundary';
import { performanceMonitor } from './utils/performance';

// Import the optimized auth hook to suppress console errors
import './hooks/useOptimizedAuth';
// Import fetch interceptor to handle 401s gracefully
import './utils/fetchInterceptor';

// Create Auth Context to share auth state across components
export const AuthContext = createContext<any>(null);

export const TextareaContext = createContext({
  textareaState: '',
  setTextareaState: (_: string) => {},
  isLnPayPending: false,
  setIsLnPayPending: (_: boolean) => {},
});

export default function App() {
  const [tooltip, setTooltip] = useState<{ x: string; y: string; text: string } | null>(null);
  const [currentText, setCurrentText] = useState<string | null>(null);
  const [textareaState, setTextareaState] = useState<string>('');
  const [isLnPayPending, setIsLnPayPending] = useState<boolean>(false);

  // Sidebar state
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
  const closeSidebar = () => setIsSidebarOpen(false);
  const toggleCollapse = () => setIsCollapsed(!isCollapsed);

  // Create sidebar context value
  const sidebarContextValue: SidebarContextType = {
    isSidebarOpen,
    toggleSidebar,
    closeSidebar,
    isCollapsed,
    toggleCollapse
  };

  const location = useLocation();
  const navigate = useNavigate();

  const { data: user, isLoading: isAuthLoading } = useAuth();

  useEffect(() => {
    if (!isAuthLoading && !user && location.pathname !== '/' && location.pathname !== '/login') {
      navigate('/login');
    }
  }, [user, isAuthLoading, location.pathname, navigate]);

  const steps = [
    { icon: FaFileAlt, label: 'Resume' },
    { icon: FaEnvelopeOpenText, label: 'Cover Letter' },
    { icon: FaBriefcase, label: 'Jobs' },
    { icon: FaComments, label: 'Interview' },
    { icon: FaChartLine, label: 'Growth' },
  ];

  useEffect(() => {
    if (isLnPayPending) {
      return;
    }
    if (!location.pathname.includes('cover-letter')) {
      setTooltip(null);
    }

    function handleMouseUp(event: any) {
      const selection = window.getSelection();

      if (selection?.toString() && location.pathname.includes('cover-letter')) {
        if (selection.toString() === currentText) {
          setTooltip(null);
          return;
        }
        setCurrentText(selection.toString());
        const x = event.clientX;
        const y = event.clientY;

        const text = selection.toString();

        setTooltip({ x, y, text });
      } else {
        setTooltip(null);
      }
    }
    function handleMouseDown() {
      if (location.pathname.includes('cover-letter')) {
        setCurrentText(null);
      }
    }

    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mousedown', handleMouseDown);
    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [tooltip, location, isLnPayPending]);

  // Initialize performance monitoring
  useEffect(() => {
    // Add test ID for E2E tests
    document.body.setAttribute('data-testid', 'app-loaded');

    // Record app initialization metric
    performanceMonitor.recordCustomMetric('APP_INIT', performance.now());
  }, []);

  return (
    <ChakraProvider theme={theme}>
      <ErrorBoundary
        onError={(error, errorInfo) => {
          // Report error to monitoring service
          console.error('Application Error:', error, errorInfo);
          performanceMonitor.recordCustomMetric('ERROR_BOUNDARY_TRIGGERED', 1, {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
          });
        }}
        showErrorDetails={process.env.NODE_ENV === 'development'}
      >
        <AuthContext.Provider value={{ data: user, isLoading: isAuthLoading }}>
          <SidebarContext.Provider value={sidebarContextValue}>
            <TextareaContext.Provider
              value={{
                textareaState,
                setTextareaState,
                isLnPayPending,
                setIsLnPayPending,
              }}
            >
        <Box
          top={tooltip?.y}
          left={tooltip?.x}
          display={tooltip?.text ? 'block' : 'none'}
          position='absolute'
          zIndex={100}
        >
          {!!user && <EditPopover setTooltip={setTooltip} user={user} />}
        </Box>
        <VStack spacing={0} align="stretch" w="full">
          <NavBar />



          <Flex w="full">
            <SidebarNav />
            <Box
              ml={{
                base: 0,
                md: location.pathname !== '/'
                  ? (isCollapsed ? '60px' : '220px')
                  : 0
              }}
              w="full"
              minH="calc(100vh - 80px)"
              bg='bg-body'
              pl={{ base: 2, md: 4 }} // Responsive padding
              transition="margin-left 0.3s ease"
              position="relative"
              zIndex="2" // Ensure content is above the sidebar
            >
              <VStack gap={5} minHeight='100%'>
                <Box flexGrow={1} width="100%" maxW="container.xl" mx="auto">
                  <Outlet />
                </Box>
                <Footer />
              </VStack>
            </Box>
          </Flex>
        </VStack>
            </TextareaContext.Provider>
          </SidebarContext.Provider>
        </AuthContext.Provider>
      </ErrorBoundary>
    </ChakraProvider>
  );
}
