import { useAuth, googleSignInUrl as signInUrl, login, signup, requestPasswordReset } from 'wasp/client/auth';
import { getLnLoginUrl, useQuery, getLnUserInfo, useAction } from 'wasp/client/operations';
import { AiOutlineGoogle } from 'react-icons/ai';
import { BsCurrencyBitcoin } from 'react-icons/bs';
import { FaShieldAlt, FaRocket, FaUsers, FaStar, FaQuoteLeft, FaCheckCircle, FaArrowRight, FaChartLine, FaBriefcase, FaGraduationCap, FaEye, FaEyeSlash } from 'react-icons/fa';
import {
  VStack,
  Button,
  Spinner,
  Text,
  useDisclosure,
  Box,
  Heading,
  Flex,
  Card,
  CardBody,
  HStack,
  Icon,
  useColorModeValue,
  Container,
  Stack,
  Badge,
  Link,
  SimpleGrid,
  Avatar,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Wrap,
  WrapItem,
  Tag,
  TagLabel,
  TagLeftIcon,
  Circle,
  Grid,
  GridItem,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Input,
  FormControl,
  FormLabel,
  FormErrorMessage,
  InputGroup,
  InputRightElement,
  IconButton,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import BorderBox from './components/BorderBox';
import { useEffect, useState } from 'react';
import UndrawIllustration, { IllustrationPresets } from './components/UndrawIllustration';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import LnLoginModal from './components/LnLoginModal';
import { useForm } from 'react-hook-form';

const MotionBox = motion(Box);
const MotionCard = motion(Card);

interface LoginForm {
  email: string;
  password: string;
}

interface SignupForm {
  email: string;
  password: string;
  confirmPassword: string;
}

export default function Login() {
  const [encodedUrl, setEncodedUrl] = useState<string | null>(null);
  const [k1Hash, setK1Hash] = useState<string>('');
  const [lnIsLoading, setLnIsLoading] = useState<boolean>(false);
  const [lnLoginStatus, setLnLoginStatus] = useState<string>('');
  const { data: lnUserInfo, refetch: fetchLnUser } = useQuery(getLnUserInfo, k1Hash, { enabled: !!k1Hash });
  const { data: user, isLoading } = useAuth();
  const { onOpen, onClose, isOpen } = useDisclosure();
  const [activeInterval, setActiveInterval] = useState<NodeJS.Timeout | null>(null);

  // New state for email/password auth
  const [activeTab, setActiveTab] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [authError, setAuthError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetEmailSent, setResetEmailSent] = useState(false);

  const getLnLoginUrlAction = useAction(getLnLoginUrl);
  const toast = useToast();
  const navigate = useNavigate();

  // Form hooks
  const loginForm = useForm<LoginForm>();
  const signupForm = useForm<SignupForm>();

  // Form submission handlers
  const handleLogin = async (data: LoginForm) => {
    setIsSubmitting(true);
    setAuthError('');

    console.log('=== LOGIN ATTEMPT ===');
    console.log('Login data:', {
      email: data.email,
      password: data.password ? '[REDACTED]' : 'undefined',
      passwordLength: data.password?.length
    });

    try {
      await login({ email: data.email, password: data.password });
      console.log('Login successful!');
      toast({
        title: 'Welcome back!',
        description: 'You have successfully signed in.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      navigate('/');
    } catch (error: any) {
      console.error('=== LOGIN ERROR ===');
      console.error('Full error object:', error);
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);

      let errorMessage = 'Login failed. Please check your credentials.';

      if (error.message) {
        const msg = error.message.toLowerCase();
        if (msg.includes('unauthorized') || msg.includes('invalid credentials')) {
          errorMessage = 'Invalid email or password. Please check your credentials.';
        } else if (msg.includes('email') && msg.includes('verified')) {
          errorMessage = 'Please verify your email address before signing in.';
        } else if (msg.includes('account') && msg.includes('not found')) {
          errorMessage = 'No account found with this email address.';
        } else {
          errorMessage = error.message;
        }
      }

      setAuthError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSignup = async (data: SignupForm) => {
    setIsSubmitting(true);
    setAuthError('');

    console.log('=== CLIENT SIGNUP ATTEMPT ===');
    console.log('Form data:', {
      email: data.email,
      password: data.password ? '[REDACTED]' : 'undefined',
      passwordLength: data.password?.length,
      confirmPassword: data.confirmPassword ? '[REDACTED]' : 'undefined'
    });

    try {
      const signupData = { email: data.email, password: data.password };
      console.log('Sending signup data:', {
        email: signupData.email,
        password: signupData.password ? '[REDACTED]' : 'undefined',
        passwordLength: signupData.password?.length
      });

      await signup(signupData);

      console.log('Signup successful!');
      toast({
        title: 'Account created!',
        description: 'Welcome to CareerDart! You can now sign in with your credentials.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Pre-fill the login form with the email
      loginForm.setValue('email', data.email);
      setActiveTab(0); // Switch to login tab
      signupForm.reset();
    } catch (error: any) {
      console.error('=== SIGNUP ERROR ===');
      console.error('Full error object:', error);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      console.error('Error response:', error.response);

      // Provide more specific error messages
      let errorMessage = 'Signup failed. Please try again.';

      if (error.message) {
        const msg = error.message.toLowerCase();
        if (msg.includes('email') && msg.includes('unique')) {
          errorMessage = 'This email address is already registered. Please try signing in instead.';
        } else if (msg.includes('email') && (msg.includes('invalid') || msg.includes('valid email'))) {
          errorMessage = 'Please enter a valid email address.';
        } else if (msg.includes('username')) {
          errorMessage = 'Unable to create a unique username. Please try a different email address.';
        } else if (msg.includes('password') && msg.includes('uppercase')) {
          errorMessage = 'Password must contain at least one uppercase letter, one lowercase letter, and one number.';
        } else if (msg.includes('password') && msg.includes('8 characters')) {
          errorMessage = 'Password must be at least 8 characters long.';
        } else if (msg.includes('password')) {
          errorMessage = 'Password must be at least 8 characters long.';
        } else if (msg.includes('validation') || msg.includes('invalid')) {
          errorMessage = 'Please check your email and password meet the requirements.';
        } else if (msg.includes('unique constraint') || msg.includes('duplicate')) {
          errorMessage = 'An account with this email already exists. Please try signing in.';
        } else {
          errorMessage = error.message;
        }
      }

      setAuthError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordReset = async (email: string) => {
    try {
      await requestPasswordReset({ email });
      setResetEmailSent(true);
      toast({
        title: 'Reset email sent!',
        description: 'Check your email for password reset instructions.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to send reset email.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    if (user) {
      navigate('/');
    }
  }, [user]);

  useEffect(() => {
    // Only fetch LN URL if we don't already have one and user is not authenticated
    if (!encodedUrl && !user) {
      try {
        const getEncodedUrl = async () => {
          const response = await getLnLoginUrlAction({});
          return response;
        };
        getEncodedUrl().then((resp) => {
          setK1Hash(resp.k1Hash);
          setEncodedUrl(resp.encoded);
        });
      } catch (error) {
        console.error('error fetching LN url: ', error);
        setEncodedUrl('error');
      }
    }
  }, [getLnLoginUrlAction, encodedUrl, user]);

  useEffect(() => {
    if (lnUserInfo?.token) {
      setLnLoginStatus('success');
      // this is how wasp stores the token for use with their auth api
      localStorage.setItem('wasp:sessionId', JSON.stringify(lnUserInfo.token));
      window.location.reload();
    }
  }, [lnUserInfo]);

  // Cleanup interval when component unmounts or user is authenticated
  useEffect(() => {
    return () => {
      if (activeInterval) {
        clearInterval(activeInterval);
      }
    };
  }, [activeInterval]);

  // Clear interval when user is authenticated
  useEffect(() => {
    if (user && activeInterval) {
      clearInterval(activeInterval);
      setActiveInterval(null);
      setLnIsLoading(false);
    }
  }, [user, activeInterval]);

  const handleWalletClick = () => {
    if (!encodedUrl) return;
    onOpen();
    setLnIsLoading(true);

    // Clear any existing interval
    if (activeInterval) {
      clearInterval(activeInterval);
    }

    // Only start polling if we don't already have a user
    if (!user) {
      const interval = setInterval(async () => {
        fetchLnUser();
      }, 5000); // Increased frequency from 2s to 5s to reduce requests
      setActiveInterval(interval);
    }

    const timeout = setTimeout(() => {
      if (!lnUserInfo?.token || !user) {
        if (activeInterval) {
          clearInterval(activeInterval);
          setActiveInterval(null);
        }
        setLnIsLoading(false);
        alert('Login timed out. Please try again.');
      }
    }, 60000);
  };

  const bgColor = useColorModeValue('white', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');

  return (
    <>
      <Box
        minH="100vh"
        bg={bgColor}
        position="relative"
      >

        <Container maxW="6xl" h="100vh" py={8}>
          <Flex
            h="full"
            align="center"
            justify="center"
            direction={{ base: 'column', lg: 'row' }}
            gap={8}
          >
            {/* Left Side - Minimalistic Welcome Content */}
            <MotionBox
              flex="1"
              maxW={{ base: 'full', lg: '500px' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <VStack spacing={12} align={{ base: 'center', lg: 'flex-start' }} textAlign={{ base: 'center', lg: 'left' }}>

                {/* Simplified Hero Section */}
                <VStack spacing={6} align={{ base: 'center', lg: 'flex-start' }}>
                  <Heading
                    size={{ base: '2xl', lg: '3xl' }}
                    color={textColor}
                    fontWeight="300"
                    lineHeight="shorter"
                    letterSpacing="-0.02em"
                  >
                    Your Career
                    <br />
                    <Text as="span" fontWeight="600">
                      Accelerated
                    </Text>
                  </Heading>

                  <Text
                    fontSize={{ base: 'md', lg: 'lg' }}
                    color={useColorModeValue("gray.600", "gray.400")}
                    maxW="400px"
                    lineHeight="relaxed"
                    fontWeight="400"
                  >
                    AI-powered tools for resumes, cover letters, and interview preparation.
                  </Text>
                </VStack>

                {/* Simple Feature List */}
                <VStack spacing={4} align={{ base: 'center', lg: 'flex-start' }} w="full" maxW="350px">
                  {[
                    "Generate professional resumes",
                    "Create tailored cover letters",
                    "Practice interview questions"
                  ].map((feature, index) => (
                    <HStack
                      key={feature}
                      spacing={3}
                      w="full"
                      justify={{ base: 'center', lg: 'flex-start' }}
                    >
                      <Box
                        w="6px"
                        h="6px"
                        borderRadius="full"
                        bg={useColorModeValue("gray.400", "gray.500")}
                        flexShrink={0}
                        mt={1}
                      />
                      <Text
                        fontSize="md"
                        color={useColorModeValue("gray.700", "gray.300")}
                        fontWeight="400"
                      >
                        {feature}
                      </Text>
                    </HStack>
                  ))}
                </VStack>

                {/* Minimal Social Proof */}
                <Text
                  fontSize="sm"
                  color={useColorModeValue("gray.500", "gray.400")}
                  fontWeight="400"
                >
                  Trusted by 10,000+ professionals
                </Text>

              </VStack>
            </MotionBox>

            {/* Right Side - Auth Form with Tabs */}
            <MotionCard
              maxW={{ base: 'md', lg: '420px' }}
              w="full"
              bg={cardBg}
              shadow="sm"
              borderRadius="xl"
              border="1px solid"
              borderColor={useColorModeValue('gray.200', 'gray.700')}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <CardBody p={8}>
                {isLoading || !encodedUrl ? (
                  <VStack spacing={6} py={8}>
                    <Spinner size="xl" color="blue.500" thickness="4px" />
                    <VStack spacing={2}>
                      <Text color={textColor} fontWeight="medium" fontSize="lg">
                        Setting up your login options...
                      </Text>
                      <Text color="gray.500" fontSize="sm">
                        This will only take a moment
                      </Text>
                    </VStack>
                  </VStack>
                ) : (
                  <Tabs
                    index={activeTab}
                    onChange={setActiveTab}
                    variant="soft-rounded"
                    colorScheme="blue"
                    isFitted
                  >
                    <TabList mb={6} bg={useColorModeValue('gray.100', 'gray.700')} borderRadius="lg" p={1}>
                      <Tab
                        borderRadius="md"
                        fontWeight="500"
                        color={useColorModeValue('gray.600', 'gray.300')}
                        _selected={{
                          bg: useColorModeValue('white', 'gray.600'),
                          color: useColorModeValue('gray.800', 'white'),
                          boxShadow: 'sm'
                        }}
                        _hover={{
                          color: useColorModeValue('gray.800', 'white')
                        }}
                      >
                        Sign In
                      </Tab>
                      <Tab
                        borderRadius="md"
                        fontWeight="500"
                        color={useColorModeValue('gray.600', 'gray.300')}
                        _selected={{
                          bg: useColorModeValue('white', 'gray.600'),
                          color: useColorModeValue('gray.800', 'white'),
                          boxShadow: 'sm'
                        }}
                        _hover={{
                          color: useColorModeValue('gray.800', 'white')
                        }}
                      >
                        Sign Up
                      </Tab>
                    </TabList>

                    <TabPanels>
                      {/* Sign In Panel */}
                      <TabPanel p={0}>
                        <VStack spacing={6}>
                          <VStack spacing={2} textAlign="center">
                            <Heading size="md" color={textColor} fontWeight="500">
                              Welcome back
                            </Heading>
                            <Text color={useColorModeValue("gray.600", "gray.400")} fontSize="sm">
                              Sign in to your account
                            </Text>
                          </VStack>

                          {authError && (
                            <Alert status="error" borderRadius="lg">
                              <AlertIcon />
                              <AlertDescription fontSize="sm">{authError}</AlertDescription>
                            </Alert>
                          )}

                          {/* Email/Password Login Form */}
                          <form onSubmit={loginForm.handleSubmit(handleLogin)} style={{ width: '100%' }}>
                            <VStack spacing={4} w="full">
                              <FormControl isInvalid={!!loginForm.formState.errors.email}>
                                <FormLabel fontSize="sm" fontWeight="500">Email</FormLabel>
                                <Input
                                  type="email"
                                  placeholder="Enter your email"
                                  size="lg"
                                  borderRadius="lg"
                                  {...loginForm.register('email', {
                                    required: 'Email is required',
                                    pattern: {
                                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                      message: 'Invalid email address'
                                    }
                                  })}
                                />
                                <FormErrorMessage>
                                  {loginForm.formState.errors.email?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <FormControl isInvalid={!!loginForm.formState.errors.password}>
                                <FormLabel fontSize="sm" fontWeight="500">Password</FormLabel>
                                <InputGroup>
                                  <Input
                                    type={showPassword ? 'text' : 'password'}
                                    placeholder="Enter your password"
                                    size="lg"
                                    borderRadius="lg"
                                    {...loginForm.register('password', {
                                      required: 'Password is required'
                                    })}
                                  />
                                  <InputRightElement h="full">
                                    <IconButton
                                      variant="ghost"
                                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                                      icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                                      onClick={() => setShowPassword(!showPassword)}
                                      size="sm"
                                    />
                                  </InputRightElement>
                                </InputGroup>
                                <FormErrorMessage>
                                  {loginForm.formState.errors.password?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <Button
                                type="submit"
                                size="lg"
                                width="full"
                                colorScheme="blue"
                                borderRadius="lg"
                                isLoading={isSubmitting}
                                loadingText="Signing in..."
                              >
                                Sign In
                              </Button>
                            </VStack>
                          </form>

                          <Text fontSize="sm" textAlign="center">
                            <Button
                              variant="link"
                              size="sm"
                              color="blue.500"
                              onClick={() => {
                                const email = loginForm.getValues('email');
                                if (email) {
                                  handlePasswordReset(email);
                                } else {
                                  toast({
                                    title: 'Enter your email first',
                                    description: 'Please enter your email address to reset your password.',
                                    status: 'info',
                                    duration: 3000,
                                    isClosable: true,
                                  });
                                }
                              }}
                            >
                              Forgot password?
                            </Button>
                          </Text>

                          <Box position="relative" w="full">
                            <Box
                              position="absolute"
                              top="50%"
                              left="0"
                              right="0"
                              h="1px"
                              bg="gray.200"
                              _dark={{ bg: 'gray.600' }}
                            />
                            <Text
                              position="relative"
                              bg={cardBg}
                              px={4}
                              color="gray.500"
                              fontSize="sm"
                              textAlign="center"
                            >
                              or continue with
                            </Text>
                          </Box>

                          <VStack spacing={3} w="full">
                            <a href={signInUrl} style={{ width: '100%' }}>
                              <Button
                                leftIcon={<AiOutlineGoogle />}
                                size="lg"
                                width="full"
                                variant="outline"
                                borderRadius="lg"
                                h="12"
                                fontSize="md"
                                fontWeight="500"
                              >
                                Google
                              </Button>
                            </a>

                            <Button
                              isLoading={lnIsLoading}
                              onClick={handleWalletClick}
                              leftIcon={<BsCurrencyBitcoin />}
                              size="lg"
                              width="full"
                              variant="outline"
                              borderRadius="lg"
                              h="12"
                              fontSize="md"
                              fontWeight="500"
                            >
                              Lightning
                              <Badge ml={2} colorScheme="orange" variant="subtle" fontSize="xs">
                                Beta
                              </Badge>
                            </Button>
                          </VStack>
                        </VStack>
                      </TabPanel>

                      {/* Sign Up Panel */}
                      <TabPanel p={0}>
                        <VStack spacing={6}>
                          <VStack spacing={2} textAlign="center">
                            <Heading size="md" color={textColor} fontWeight="500">
                              Create account
                            </Heading>
                            <Text color={useColorModeValue("gray.600", "gray.400")} fontSize="sm">
                              Join CareerDart today
                            </Text>
                          </VStack>

                          {authError && (
                            <Alert status="error" borderRadius="lg">
                              <AlertIcon />
                              <AlertDescription fontSize="sm">{authError}</AlertDescription>
                            </Alert>
                          )}

                          {/* Email/Password Signup Form */}
                          <form onSubmit={signupForm.handleSubmit(handleSignup)} style={{ width: '100%' }}>
                            <VStack spacing={4} w="full">
                              <FormControl isInvalid={!!signupForm.formState.errors.email}>
                                <FormLabel fontSize="sm" fontWeight="500">Email</FormLabel>
                                <Input
                                  type="email"
                                  placeholder="Enter your email"
                                  size="lg"
                                  borderRadius="lg"
                                  {...signupForm.register('email', {
                                    required: 'Email is required',
                                    pattern: {
                                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                      message: 'Invalid email address'
                                    }
                                  })}
                                />
                                <FormErrorMessage>
                                  {signupForm.formState.errors.email?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <FormControl isInvalid={!!signupForm.formState.errors.password}>
                                <FormLabel fontSize="sm" fontWeight="500">Password</FormLabel>
                                <InputGroup>
                                  <Input
                                    type={showPassword ? 'text' : 'password'}
                                    placeholder="Create a password"
                                    size="lg"
                                    borderRadius="lg"
                                    {...signupForm.register('password', {
                                      required: 'Password is required',
                                      minLength: {
                                        value: 8,
                                        message: 'Password must be at least 8 characters'
                                      },
                                      validate: {
                                        hasUppercase: (value) =>
                                          /[A-Z]/.test(value) || 'Password must contain at least one uppercase letter',
                                        hasLowercase: (value) =>
                                          /[a-z]/.test(value) || 'Password must contain at least one lowercase letter',
                                        hasNumber: (value) =>
                                          /[0-9]/.test(value) || 'Password must contain at least one number'
                                      }
                                    })}
                                  />
                                  <InputRightElement h="full">
                                    <IconButton
                                      variant="ghost"
                                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                                      icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                                      onClick={() => setShowPassword(!showPassword)}
                                      size="sm"
                                    />
                                  </InputRightElement>
                                </InputGroup>
                                <FormErrorMessage>
                                  {signupForm.formState.errors.password?.message}
                                </FormErrorMessage>
                                {!signupForm.formState.errors.password && (
                                  <Text fontSize="xs" color="gray.500" mt={1}>
                                    Must be at least 8 characters with uppercase, lowercase, and number
                                  </Text>
                                )}
                              </FormControl>

                              <FormControl isInvalid={!!signupForm.formState.errors.confirmPassword}>
                                <FormLabel fontSize="sm" fontWeight="500">Confirm Password</FormLabel>
                                <InputGroup>
                                  <Input
                                    type={showConfirmPassword ? 'text' : 'password'}
                                    placeholder="Confirm your password"
                                    size="lg"
                                    borderRadius="lg"
                                    {...signupForm.register('confirmPassword', {
                                      required: 'Please confirm your password',
                                      validate: (value) =>
                                        value === signupForm.watch('password') || 'Passwords do not match'
                                    })}
                                  />
                                  <InputRightElement h="full">
                                    <IconButton
                                      variant="ghost"
                                      aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                                      icon={showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                      size="sm"
                                    />
                                  </InputRightElement>
                                </InputGroup>
                                <FormErrorMessage>
                                  {signupForm.formState.errors.confirmPassword?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <Button
                                type="submit"
                                size="lg"
                                width="full"
                                colorScheme="blue"
                                borderRadius="lg"
                                isLoading={isSubmitting}
                                loadingText="Creating account..."
                              >
                                Create Account
                              </Button>
                            </VStack>
                          </form>

                          <Box position="relative" w="full">
                            <Box
                              position="absolute"
                              top="50%"
                              left="0"
                              right="0"
                              h="1px"
                              bg="gray.200"
                              _dark={{ bg: 'gray.600' }}
                            />
                            <Text
                              position="relative"
                              bg={cardBg}
                              px={4}
                              color="gray.500"
                              fontSize="sm"
                              textAlign="center"
                            >
                              or sign up with
                            </Text>
                          </Box>

                          <VStack spacing={3} w="full">
                            <a href={signInUrl} style={{ width: '100%' }}>
                              <Button
                                leftIcon={<AiOutlineGoogle />}
                                size="lg"
                                width="full"
                                variant="outline"
                                borderRadius="lg"
                                h="12"
                                fontSize="md"
                                fontWeight="500"
                              >
                                Google
                              </Button>
                            </a>

                            <Button
                              isLoading={lnIsLoading}
                              onClick={handleWalletClick}
                              leftIcon={<BsCurrencyBitcoin />}
                              size="lg"
                              width="full"
                              variant="outline"
                              borderRadius="lg"
                              h="12"
                              fontSize="md"
                              fontWeight="500"
                            >
                              Lightning
                              <Badge ml={2} colorScheme="orange" variant="subtle" fontSize="xs">
                                Beta
                              </Badge>
                            </Button>
                          </VStack>
                        </VStack>
                      </TabPanel>
                    </TabPanels>

                    <Text fontSize="xs" color="gray.500" textAlign="center" mt={6}>
                      By continuing, you agree to our{' '}
                      <Link as={RouterLink} to="/tos" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                        Terms of Service
                      </Link>
                      {' '}and{' '}
                      <Link as={RouterLink} to="/privacy" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                        Privacy Policy
                      </Link>
                    </Text>
                  </Tabs>
                )}
              </CardBody>
            </MotionCard>
          </Flex>
        </Container>
      </Box>

      <LnLoginModal
        handleWalletClick={handleWalletClick}
        status={lnLoginStatus}
        encodedUrl={encodedUrl}
        isOpen={isOpen}
        onClose={onClose}
      />
    </>
  );
}
