import { useState, useRef } from 'react';
import { HStack, FormControl, Input, useToast } from '@chakra-ui/react';
import { FaUpload, FaPlus } from 'react-icons/fa';
import { CloseIcon } from '@chakra-ui/icons';
import { useAuth } from 'wasp/client/auth';
import { createResume } from 'wasp/client/operations';
import ResumeManager from './components/ResumeManager';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ContentContainer from './components/ContentContainer';
import ActionButton from './components/ActionButton';
import { type Resume } from '../shared/types';

export default function ResumePage() {
  const [isCreatingResume, setIsCreatingResume] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();
  const { data: user } = useAuth();

  const handleFileButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a PDF, Word document, or image file.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Read file as base64 for storage
    const reader = new FileReader();
    reader.onload = async (e) => {
      if (!e.target || typeof e.target.result !== 'string') return;

      const base64Data = e.target.result.split(',')[1]; // Remove data URL prefix

      // Create a new resume entry with file data
      const newResume: Partial<Resume> = {
        title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
        personalInfo: {
          fullName: '',
          email: '',
          phone: '',
          location: '',
        },
        summary: 'Uploaded Resume',
        experience: [],
        education: [],
        skills: [],
        fileData: {
          name: file.name,
          type: file.type,
          data: base64Data
        }
      };

      try {
        await createResume(newResume);
        toast({
          title: 'Resume uploaded',
          description: 'Your resume has been uploaded successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to upload resume. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    };

    reader.readAsDataURL(file);
  };

  const handleCreateResume = () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to create resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    setIsCreatingResume(true);
  };

  const handleCancelCreate = () => {
    setIsCreatingResume(false);
  };

  return (
    <ContentPageBox>
      <PageHeader
        title="Resume Manager"
        subtitle="Upload, create and manage your resumes"
      >
        <HStack spacing={3}>
          <FormControl width="auto">
            <ActionButton
              icon={FaUpload}
              label="Upload"
              variant="outline"
              onClick={handleFileButtonClick}
              isDisabled={isCreatingResume}
              minW="80px"
            />
          </FormControl>
          <ActionButton
            icon={isCreatingResume ? undefined : FaPlus}
            label={isCreatingResume ? "Cancel" : "Create New"}
            variant={isCreatingResume ? "outline" : "primary"}
            onClick={isCreatingResume ? handleCancelCreate : handleCreateResume}
            leftIcon={isCreatingResume ? <CloseIcon /> : undefined}
            minW="100px"
          />
        </HStack>
      </PageHeader>
      <ContentContainer noPadding>
        <ResumeManager
          onFileUpload={handleFileUpload}
          onCreateResume={handleCreateResume}
          onCancelCreate={handleCancelCreate}
          isCreatingResume={isCreatingResume}
          fileInputRef={fileInputRef}
        />
      </ContentContainer>
    </ContentPageBox>
  );
}
