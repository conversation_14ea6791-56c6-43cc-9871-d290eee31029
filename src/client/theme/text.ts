const FONT_SCALE_BASE = 1;
const FONT_SCALE_MULTIPLIER = 1.25;

export const fonts = {
  heading: 'Inter, system-ui, sans-serif',
  body: 'Inter, system-ui, sans-serif',
};

export const textStyles = {
  h1: {
    fontSize: ['2xl', '3xl', '4xl'],
    fontWeight: 'bold',
    lineHeight: '1.2',
    letterSpacing: 'tight',
    color: 'text-contrast-xl',
  },
  h2: {
    fontSize: ['xl', '2xl', '3xl'],
    fontWeight: 'bold',
    lineHeight: '1.3',
    letterSpacing: 'tight',
    color: 'text-contrast-xl',
  },
  h3: {
    fontSize: ['lg', 'xl', '2xl'],
    fontWeight: 'semibold',
    lineHeight: '1.4',
    letterSpacing: 'tight',
    color: 'text-contrast-lg',
  },
  h4: {
    fontSize: ['md', 'lg', 'xl'],
    fontWeight: 'semibold',
    lineHeight: '1.4',
    letterSpacing: 'tight',
    color: 'text-contrast-lg',
  },
  subtitle1: {
    fontSize: ['sm', 'md', 'lg'],
    fontWeight: 'medium',
    lineHeight: '1.5',
    letterSpacing: 'normal',
    color: 'text-contrast-md',
  },
  subtitle2: {
    fontSize: ['xs', 'sm', 'md'],
    fontWeight: 'medium',
    lineHeight: '1.5',
    letterSpacing: 'normal',
    color: 'text-contrast-md',
  },
  body1: {
    fontSize: ['sm', 'md', 'lg'],
    fontWeight: 'normal',
    lineHeight: '1.6',
    letterSpacing: 'normal',
    color: 'text-contrast-lg',
  },
  body2: {
    fontSize: ['xs', 'sm', 'md'],
    fontWeight: 'normal',
    lineHeight: '1.6',
    letterSpacing: 'normal',
    color: 'text-contrast-lg',
  },
  button: {
    fontSize: ['sm', 'md'],
    fontWeight: 'semibold',
    lineHeight: '1.5',
    letterSpacing: 'wide',
    textTransform: 'uppercase',
  },
  caption: {
    fontSize: ['xs', 'sm'],
    fontWeight: 'normal',
    lineHeight: '1.5',
    letterSpacing: 'normal',
    color: 'text-contrast-sm',
  },
  overline: {
    fontSize: ['xs'],
    fontWeight: 'medium',
    lineHeight: '1.5',
    letterSpacing: 'wider',
    textTransform: 'uppercase',
    color: 'text-contrast-sm',
  },
};
