import { extendTheme } from '@chakra-ui/react';
import { StyleFunctionProps } from '@chakra-ui/theme-tools';
import { textStyles, fonts } from './text';
import { semanticTokens } from './tokens';
import {
  Button as ChakraButton,
} from '@chakra-ui/react';

const variantSolid = (props: any) => {
  const { colorScheme: c } = props;

  let bg = `${c}.500`;
  let color = 'white';
  let hoverBg = `${c}.600`;
  let activeBg = `${c}.700`;
  let disabledBg = `${c}.300`;

  if (c === 'contrast') {
    bg = 'primary';
    color = 'white';
    hoverBg = 'primary';
    activeBg = 'primary';
    disabledBg = 'gray.300';
  }

  return {
    border: 'none',
    bgColor: bg,
    color: color,
    _hover: {
      bg: hoverBg,
      transform: 'translateY(-1px)',
      boxShadow: 'md',
    },
    _focus: {
      boxShadow: 'none',
      borderColor: 'active',
    },
    _disabled: {
      bg: disabledBg,
      pointerEvents: 'none',
      opacity: 0.66,
    },
    _active: {
      bg: activeBg,
      transform: 'translateY(0)',
    },
  };
};

export const Button = {
  defaultProps: {
    colorScheme: 'blue',
  },
  baseStyle: {
    fontWeight: '600',
    borderRadius: 'xl',
    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    letterSpacing: '0.025em',
    _focus: {
      boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.15)',
      outline: 'none',
    },
    _disabled: {
      opacity: 0.6,
      cursor: 'not-allowed',
      transform: 'none',
    },
  },
  sizes: {
    xs: {
      h: '6',
      minW: '6',
      fontSize: 'xs',
      px: '2',
    },
    sm: {
      h: '8',
      minW: '8',
      fontSize: 'sm',
      px: '3',
    },
    md: {
      h: '10',
      minW: '10',
      fontSize: 'md',
      px: '4',
    },
    lg: {
      h: '12',
      minW: '12',
      fontSize: 'lg',
      px: '6',
    },
    xl: {
      h: '14',
      minW: '14',
      fontSize: 'xl',
      px: '8',
    },
  },
  variants: {
    solid: {
      bg: 'blue.500',
      color: 'white',
      _hover: {
        bg: 'blue.600',
        transform: 'translateY(-1px)',
        boxShadow: 'lg',
      },
      _active: {
        bg: 'blue.700',
        transform: 'translateY(0)',
      },
    },
    outline: {
      border: '1.5px solid',
      borderColor: 'currentColor',
      bg: 'transparent',
      _hover: {
        bg: 'rgba(66, 153, 225, 0.06)',
        transform: 'translateY(-1px)',
        boxShadow: 'md',
        borderColor: 'blue.500',
      },
      _active: {
        bg: 'rgba(66, 153, 225, 0.12)',
        transform: 'translateY(0)',
      },
    },
    ghost: {
      bg: 'transparent',
      _hover: {
        bg: 'rgba(66, 153, 225, 0.06)',
        transform: 'translateY(-1px)',
      },
      _active: {
        bg: 'rgba(66, 153, 225, 0.12)',
        transform: 'translateY(0)',
      },
    },
    link: {
      padding: 0,
      height: 'auto',
      lineHeight: 'normal',
      verticalAlign: 'baseline',
      color: 'blue.500',
      _hover: {
        textDecoration: 'underline',
        color: 'blue.600',
      },
    },
  },
};

ChakraButton.defaultProps = {
  ...ChakraButton.defaultProps,
  fontSize: 'md',
  variant: 'solid',
  size: 'md',
};

export const Input = {
  baseStyle: {
    field: {
      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
  sizes: {
    sm: {
      field: {
        borderRadius: 'lg',
        fontSize: 'sm',
        h: '9',
        px: '3',
      },
    },
    md: {
      field: {
        borderRadius: 'lg',
        fontSize: 'md',
        h: '10',
        px: '4',
      },
    },
    lg: {
      field: {
        borderRadius: 'lg',
        fontSize: 'lg',
        h: '12',
        px: '4',
      },
    },
  },
  variants: {
    outline: {
      field: {
        border: '1.5px solid',
        borderColor: 'gray.200',
        bg: 'white',
        color: 'gray.900',
        _dark: {
          borderColor: 'gray.600',
          bg: 'gray.800',
          color: 'white',
        },
        _hover: {
          borderColor: 'gray.300',
          _dark: {
            borderColor: 'gray.500',
          },
        },
        _focus: {
          borderColor: 'blue.500',
          boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.1)',
          bg: 'white',
          _dark: {
            bg: 'gray.800',
            borderColor: 'blue.400',
            boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.15)',
          },
        },
        _invalid: {
          borderColor: 'red.500',
          boxShadow: '0 0 0 3px rgba(245, 101, 101, 0.1)',
        },
        _disabled: {
          bg: 'gray.50',
          borderColor: 'gray.200',
          opacity: 0.6,
          cursor: 'not-allowed',
          _dark: {
            bg: 'gray.700',
            borderColor: 'gray.600',
          },
        },
        _placeholder: {
          color: 'gray.500',
          _dark: {
            color: 'gray.400',
          },
        },
      },
    },
    filled: {
      field: {
        border: '1.5px solid transparent',
        bg: 'gray.50',
        color: 'gray.900',
        _dark: {
          bg: 'gray.700',
          color: 'white',
        },
        _hover: {
          bg: 'gray.100',
          _dark: {
            bg: 'gray.600',
          },
        },
        _focus: {
          bg: 'white',
          borderColor: 'blue.500',
          boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.1)',
          _dark: {
            bg: 'gray.800',
            borderColor: 'blue.400',
            boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.15)',
          },
        },
      },
    },
  },
};

export const Textarea = {
  baseStyle: {
    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    resize: 'vertical',
  },
  sizes: {
    sm: {
      borderRadius: 'lg',
      fontSize: 'sm',
      px: '3',
      py: '2',
    },
    md: {
      borderRadius: 'lg',
      fontSize: 'md',
      px: '4',
      py: '3',
    },
    lg: {
      borderRadius: 'lg',
      fontSize: 'lg',
      px: '4',
      py: '3',
    },
  },
  variants: {
    outline: {
      border: '1.5px solid',
      borderColor: 'gray.200',
      bg: 'white',
      color: 'gray.900',
      _dark: {
        borderColor: 'gray.600',
        bg: 'gray.800',
        color: 'white',
      },
      _hover: {
        borderColor: 'gray.300',
        _dark: {
          borderColor: 'gray.500',
        },
      },
      _focus: {
        borderColor: 'blue.500',
        boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.1)',
        bg: 'white',
        _dark: {
          bg: 'gray.800',
          borderColor: 'blue.400',
          boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.15)',
        },
      },
      _invalid: {
        borderColor: 'red.500',
        boxShadow: '0 0 0 3px rgba(245, 101, 101, 0.1)',
      },
      _disabled: {
        bg: 'gray.50',
        borderColor: 'gray.200',
        opacity: 0.6,
        cursor: 'not-allowed',
        _dark: {
          bg: 'gray.700',
          borderColor: 'gray.600',
        },
      },
      _placeholder: {
        color: 'gray.500',
        _dark: {
          color: 'gray.400',
        },
      },
    },
    filled: {
      border: '1.5px solid transparent',
      bg: 'gray.50',
      color: 'gray.900',
      _dark: {
        bg: 'gray.700',
        color: 'white',
      },
      _hover: {
        bg: 'gray.100',
        _dark: {
          bg: 'gray.600',
        },
      },
      _focus: {
        bg: 'white',
        borderColor: 'blue.500',
        boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.1)',
        _dark: {
          bg: 'gray.800',
          borderColor: 'blue.400',
          boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.15)',
        },
      },
    },
  },
};

export const Select = {
  variants: {
    outline: {
      field: {
        border: '1px solid',
        borderColor: 'border-contrast-sm',
        bg: 'bg-contrast-xs',
        color: 'text-contrast-lg',
        borderRadius: 'md',
        fontSize: 'sm',
        height: '40px',
        transition: 'all 0.2s ease-in-out',
        _hover: {
          borderColor: 'border-contrast-md',
        },
        _focus: {
          borderColor: 'primary',
          boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
        },
        _disabled: {
          bg: 'bg-contrast-xs',
          opacity: 0.7,
        },
      },
    },
  },
};

export const Checkbox = {
  baseStyle: {
    control: {
      border: '1px solid',
      borderColor: 'border-contrast-md',
      bg: 'bg-contrast-xs',
      borderRadius: 'sm',
      transition: 'all 0.2s ease-in-out',
      _hover: {
        borderColor: 'primary',
      },
      _focus: {
        boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
        borderColor: 'primary',
      },
      _disabled: {
        bg: 'bg-contrast-xs',
        opacity: 0.7,
      },
      _checked: {
        bg: 'primary',
        borderColor: 'primary',
      }
    },
    label: {
      fontSize: 'sm',
      fontWeight: 'normal',
    }
  },
};

export const Radio = {
  baseStyle: {
    control: {
      border: '1px solid',
      borderColor: 'border-contrast-md',
      bg: 'bg-contrast-xs',
      transition: 'all 0.2s ease-in-out',
      _hover: {
        borderColor: 'primary',
      },
      _focus: {
        boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
        borderColor: 'primary',
      },
      _disabled: {
        bg: 'bg-contrast-xs',
        opacity: 0.7,
      },
      _checked: {
        bg: 'primary',
        borderColor: 'primary',
      }
    },
    label: {
      fontSize: 'sm',
      fontWeight: 'normal',
    }
  },
};

export const Link = {
  baseStyle: {
    color: 'primary',
    fontWeight: 'medium',
    transition: 'all 0.2s ease-in-out',
    _hover: {
      textDecoration: 'none',
      color: 'blue.600',
      transform: 'translateY(-1px)',
    },
    _focus: {
      boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
      outline: 'none',
    },
    _active: {
      opacity: 0.8,
      transform: 'translateY(0)',
    },
  },
};

const config = {
  initialColorMode: 'light',
  useSystemColorMode: false,
};

const keyframes = {
  fadeIn: {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' },
  },
  slideUp: {
    '0%': { transform: 'translateY(20px)', opacity: '0' },
    '100%': { transform: 'translateY(0)', opacity: '1' },
  },
  slideDown: {
    '0%': { transform: 'translateY(-20px)', opacity: '0' },
    '100%': { transform: 'translateY(0)', opacity: '1' },
  },
  scale: {
    '0%': { transform: 'scale(0.95)', opacity: '0' },
    '100%': { transform: 'scale(1)', opacity: '1' },
  },
};

export const styles = {
  global: (_props: StyleFunctionProps) => ({
    html: {
      fontSize: {
        base: '90%',
        md: '100%',
      },
      scrollBehavior: 'smooth',
    },
    body: {
      bgColor: 'bg-body',
      color: 'text-contrast-lg',
      transition: 'background-color 0.2s ease-in-out',
    },
    '*::placeholder': {
      color: 'text-contrast-sm',
    },
    '*, *::before, &::after': {
      borderColor: 'border-contrast-sm',
    },
    '@keyframes fadeIn': keyframes.fadeIn,
    '@keyframes slideUp': keyframes.slideUp,
    '@keyframes slideDown': keyframes.slideDown,
    '@keyframes scale': keyframes.scale,
  }),
};

export const theme = extendTheme({
  components: {
    Input,
    Textarea,
    Button,
    Checkbox,
    Radio,
    Select,
    Link,
  },
  config,
  styles,
  semanticTokens,
  fonts,
  textStyles,
  keyframes,
});
