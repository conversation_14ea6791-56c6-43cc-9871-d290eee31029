import { type CoverLetter } from 'wasp/entities';
import { useQuery, getAllCoverLetters } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Heading,
  VStack,
  HStack,
  Text,
  Badge,
  Grid,
  GridItem,
  Tooltip,
} from '@chakra-ui/react';
import { FaPlus, FaEdit, FaCopy } from 'react-icons/fa';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ActionButton from './components/ActionButton';
import ContentContainer from './components/ContentContainer';
import { useState, useEffect } from 'react';
import { useClipboard } from '@chakra-ui/react';
import { EmptyCoverLetters } from './components/EmptyState';
import { SimpleLoading } from './components/LoadingState';

export default function CoverLettersPage() {
  const navigate = useNavigate();
  const [selectedCoverLetter, setSelectedCoverLetter] = useState<CoverLetter | null>(null);
  const { onCopy } = useClipboard(selectedCoverLetter?.content || '');
  const { data: user } = useAuth();

  const { data: coverLetters, isLoading } = useQuery(getAllCoverLetters, undefined, { enabled: !!user });

  // Set the first cover letter as selected when data loads
  useEffect(() => {
    if (coverLetters && coverLetters.length > 0 && !selectedCoverLetter) {
      setSelectedCoverLetter(coverLetters[0]);
    }
  }, [coverLetters]);

  const handleCreateNew = () => {
    navigate('/');
  };

  const handleEdit = (id: string) => {
    navigate(`/cover-letters/${id}`);
  };

  const handlePreview = (coverLetter: CoverLetter) => {
    setSelectedCoverLetter(coverLetter);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString() + ' ' + new Date(date).toLocaleTimeString();
  };

  return (
    <ContentPageBox>
      <VStack spacing={6} align="stretch" width="100%">
        <PageHeader
          title="Cover Letters"
          subtitle="Manage all your cover letters in one place"
        >
          <ActionButton
            icon={FaPlus}
            label="Create New"
            variant="primary"
            onClick={handleCreateNew}
          />
        </PageHeader>

        {isLoading ? (
          <SimpleLoading message="Loading your cover letters..." />
        ) : !coverLetters || coverLetters.length === 0 ? (
          <EmptyCoverLetters
            primaryAction={{
              label: 'Create Cover Letter',
              onClick: handleCreateNew,
              icon: <FaPlus />,
              colorScheme: 'blue',
            }}
          />
        ) : (
          <Grid templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }} gap={6}>
            {/* Left side - List of cover letters */}
            <GridItem>
              <ContentContainer delay={0.3} maxH="calc(100vh - 200px)" overflowY="auto">
                <VStack spacing={2} align="stretch">
                  {coverLetters.map((coverLetter) => (
                  <Box
                    key={coverLetter.id}
                    p={3}
                    borderRadius="md"
                    borderLeft={coverLetter.id === selectedCoverLetter?.id ? '2px solid' : 'none'}
                    borderColor={coverLetter.id === selectedCoverLetter?.id ? 'purple.400' : 'transparent'}
                    bg={coverLetter.id === selectedCoverLetter?.id ? 'gray.50' : 'transparent'}
                    cursor="pointer"
                    onClick={() => handlePreview(coverLetter)}
                    _hover={{ bg: 'gray.50' }}
                    transition="all 0.15s"
                    boxShadow="none"
                    mb={1}
                  >
                    <VStack align="stretch" spacing={2}>
                      <HStack justify="space-between" align="flex-start">
                        <Heading size="sm" fontWeight="medium">{coverLetter.title}</Heading>
                        <Badge colorScheme="purple" fontSize="xs" variant="subtle">
                          {formatDate(coverLetter.createdAt)}
                        </Badge>
                      </HStack>
                      <Text noOfLines={1} fontSize="sm" color="gray.600">
                        {coverLetter.content.substring(0, 80)}...
                      </Text>
                      <HStack spacing={2} justify="flex-end">
                        <Tooltip label="Edit Cover Letter">
                          <span>
                            <ActionButton
                              icon={FaEdit}
                              label=""
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(coverLetter.id);
                              }}
                            />
                          </span>
                        </Tooltip>
                        <Tooltip label="Copy to Clipboard">
                          <span>
                            <ActionButton
                              icon={FaCopy}
                              label=""
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                onCopy();
                              }}
                            />
                          </span>
                        </Tooltip>
                      </HStack>
                    </VStack>
                  </Box>
                ))}
                </VStack>
              </ContentContainer>
            </GridItem>

            {/* Right side - Cover letter preview */}
            <GridItem>
              {selectedCoverLetter ? (
                <ContentContainer
                  height="calc(100vh - 200px)"
                  overflowY="auto"
                  delay={0.4}
                >
                  <VStack align="stretch" spacing={6}>
                    <Box>
                      <Heading size="sm" fontWeight="medium" mb={3}>Cover Letter Content</Heading>
                      <Text whiteSpace="pre-wrap" fontSize="sm">{selectedCoverLetter.content}</Text>
                    </Box>
                  </VStack>
                </ContentContainer>
              ) : (
                <ContentContainer
                  height="calc(100vh - 200px)"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  delay={0.4}
                >
                  <Text fontSize="sm" color="gray.500">
                    Select a cover letter from the list to preview its content
                  </Text>
                </ContentContainer>
              )}
            </GridItem>
          </Grid>
        )}
      </VStack>
    </ContentPageBox>
  );
}