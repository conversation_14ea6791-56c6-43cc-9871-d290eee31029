import React from 'react';
import {
  <PERSON>,
  Container,
  VStack,
  <PERSON>ing,
  Text,
  Button,
  useColorModeValue,
  Icon,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import { Link as RouterLink, useSearchParams } from 'react-router-dom';
import { verifyEmail } from 'wasp/client/auth';
import { useState, useEffect } from 'react';

export default function EmailVerification() {
  const [searchParams] = useSearchParams();
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState('');

  const bgColor = useColorModeValue('white', 'gray.900');
  const textColor = useColorModeValue('gray.700', 'gray.200');

  useEffect(() => {
    const token = searchParams.get('token');
    
    if (!token) {
      setVerificationStatus('error');
      setErrorMessage('Invalid verification link. Please check your email and try again.');
      return;
    }

    const verify = async () => {
      try {
        await verifyEmail({ token });
        setVerificationStatus('success');
      } catch (error: any) {
        setVerificationStatus('error');
        setErrorMessage(error.message || 'Verification failed. The link may have expired.');
      }
    };

    verify();
  }, [searchParams]);

  return (
    <Box minH="100vh" bg={bgColor} py={16}>
      <Container maxW="md">
        <VStack spacing={8} textAlign="center">
          {verificationStatus === 'loading' && (
            <>
              <Icon as={FaCheckCircle} boxSize={16} color="blue.500" />
              <Heading size="lg" color={textColor}>
                Verifying your email...
              </Heading>
              <Text color={useColorModeValue("gray.600", "gray.400")}>
                Please wait while we verify your email address.
              </Text>
            </>
          )}

          {verificationStatus === 'success' && (
            <>
              <Icon as={FaCheckCircle} boxSize={16} color="green.500" />
              <Heading size="lg" color={textColor}>
                Email Verified Successfully!
              </Heading>
              <Text color={useColorModeValue("gray.600", "gray.400")}>
                Your email has been verified. You can now sign in to your CareerDart account.
              </Text>
              <Button
                as={RouterLink}
                to="/login"
                colorScheme="blue"
                size="lg"
                borderRadius="xl"
              >
                Sign In to CareerDart
              </Button>
            </>
          )}

          {verificationStatus === 'error' && (
            <>
              <Icon as={FaExclamationTriangle} boxSize={16} color="red.500" />
              <Heading size="lg" color={textColor}>
                Verification Failed
              </Heading>
              <Alert status="error" borderRadius="xl">
                <AlertIcon />
                <Box>
                  <AlertTitle>Error!</AlertTitle>
                  <AlertDescription>{errorMessage}</AlertDescription>
                </Box>
              </Alert>
              <VStack spacing={4}>
                <Button
                  as={RouterLink}
                  to="/login"
                  colorScheme="blue"
                  variant="outline"
                  size="lg"
                  borderRadius="xl"
                >
                  Back to Login
                </Button>
                <Text fontSize="sm" color={useColorModeValue("gray.500", "gray.400")}>
                  Need help? Contact our support team.
                </Text>
              </VStack>
            </>
          )}
        </VStack>
      </Container>
    </Box>
  );
}
