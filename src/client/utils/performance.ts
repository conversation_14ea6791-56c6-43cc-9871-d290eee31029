// Performance monitoring utilities
import React from 'react';

// Type declarations for missing performance APIs
declare global {
  interface LayoutShift extends PerformanceEntry {
    value: number;
    hadRecentInput: boolean;
  }

  interface PerformanceLongTaskTiming extends PerformanceEntry {
    attribution: any[];
  }
}

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
}

interface VitalMetric extends PerformanceMetric {
  rating: 'good' | 'needs-improvement' | 'poor';
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private isEnabled: boolean;
  private reportingEndpoint: string;

  constructor(options: {
    enabled?: boolean;
    reportingEndpoint?: string;
  } = {}) {
    this.isEnabled = options.enabled ?? process.env.NODE_ENV === 'production';
    this.reportingEndpoint = options.reportingEndpoint ?? '/api/performance';

    if (this.isEnabled && typeof window !== 'undefined') {
      this.initializeMonitoring();
    }
  }

  private initializeMonitoring() {
    // Monitor Core Web Vitals
    this.observeCLS();
    this.observeFID();
    this.observeFCP();
    this.observeLCP();
    this.observeTTFB();

    // Monitor custom metrics
    this.observeNavigationTiming();
    this.observeResourceTiming();
    this.observeLongTasks();

    // Report metrics periodically
    this.startPeriodicReporting();

    // Report on page unload
    this.setupUnloadReporting();
  }

  // Cumulative Layout Shift
  private observeCLS() {
    if (!('LayoutShift' in window)) return;

    let clsValue = 0;
    let clsEntries: LayoutShift[] = [];

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries() as LayoutShift[]) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          clsEntries.push(entry);
        }
      }

      this.recordVital('CLS', clsValue, this.getRating('CLS', clsValue));
    });

    observer.observe({ type: 'layout-shift', buffered: true });
  }

  // First Input Delay
  private observeFID() {
    if (!('PerformanceEventTiming' in window)) return;

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries() as PerformanceEventTiming[]) {
        if (entry.name === 'first-input') {
          const fid = entry.processingStart - entry.startTime;
          this.recordVital('FID', fid, this.getRating('FID', fid));
        }
      }
    });

    observer.observe({ type: 'first-input', buffered: true });
  }

  // First Contentful Paint
  private observeFCP() {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.recordVital('FCP', entry.startTime, this.getRating('FCP', entry.startTime));
        }
      }
    });

    observer.observe({ type: 'paint', buffered: true });
  }

  // Largest Contentful Paint
  private observeLCP() {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.recordVital('LCP', lastEntry.startTime, this.getRating('LCP', lastEntry.startTime));
    });

    observer.observe({ type: 'largest-contentful-paint', buffered: true });
  }

  // Time to First Byte
  private observeTTFB() {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries() as PerformanceNavigationTiming[]) {
        const ttfb = entry.responseStart - entry.requestStart;
        this.recordVital('TTFB', ttfb, this.getRating('TTFB', ttfb));
      }
    });

    observer.observe({ type: 'navigation', buffered: true });
  }

  // Navigation Timing
  private observeNavigationTiming() {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries() as PerformanceNavigationTiming[]) {
        this.recordMetric('DOM_CONTENT_LOADED', entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart);
        this.recordMetric('LOAD_EVENT', entry.loadEventEnd - entry.loadEventStart);
        this.recordMetric('DNS_LOOKUP', entry.domainLookupEnd - entry.domainLookupStart);
        this.recordMetric('TCP_CONNECT', entry.connectEnd - entry.connectStart);
        this.recordMetric('REQUEST_RESPONSE', entry.responseEnd - entry.requestStart);
      }
    });

    observer.observe({ type: 'navigation', buffered: true });
  }

  // Resource Timing
  private observeResourceTiming() {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries() as PerformanceResourceTiming[]) {
        // Track slow resources
        const duration = entry.responseEnd - entry.startTime;
        if (duration > 1000) { // Resources taking more than 1 second
          this.recordMetric('SLOW_RESOURCE', duration, {
            url: entry.name,
            type: entry.initiatorType,
            size: entry.transferSize,
          });
        }
      }
    });

    observer.observe({ type: 'resource', buffered: true });
  }

  // Long Tasks
  private observeLongTasks() {
    if (!('PerformanceLongTaskTiming' in window)) return;

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries() as PerformanceLongTaskTiming[]) {
        this.recordMetric('LONG_TASK', entry.duration, {
          startTime: entry.startTime,
          attribution: entry.attribution,
        });
      }
    });

    observer.observe({ type: 'longtask', buffered: true });
  }

  private getRating(metric: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = {
      CLS: { good: 0.1, poor: 0.25 },
      FID: { good: 100, poor: 300 },
      FCP: { good: 1800, poor: 3000 },
      LCP: { good: 2500, poor: 4000 },
      TTFB: { good: 800, poor: 1800 },
    };

    const threshold = thresholds[metric as keyof typeof thresholds];
    if (!threshold) return 'good';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  private recordMetric(name: string, value: number, metadata?: any) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...metadata,
    };

    this.metrics.push(metric);
  }

  private recordVital(name: string, value: number, rating: 'good' | 'needs-improvement' | 'poor') {
    const vital: VitalMetric = {
      name,
      value,
      rating,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    this.metrics.push(vital);
  }

  private startPeriodicReporting() {
    // Report metrics every 30 seconds
    setInterval(() => {
      this.reportMetrics();
    }, 30000);
  }

  private setupUnloadReporting() {
    // Report metrics when page is unloaded
    window.addEventListener('beforeunload', () => {
      this.reportMetrics(true);
    });

    // Use Page Visibility API for better coverage
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.reportMetrics(true);
      }
    });
  }

  private async reportMetrics(isUnloading = false) {
    if (this.metrics.length === 0) return;

    const metricsToReport = [...this.metrics];
    this.metrics = [];

    try {
      if (isUnloading && 'sendBeacon' in navigator) {
        // Use sendBeacon for reliable reporting during page unload
        navigator.sendBeacon(
          this.reportingEndpoint,
          JSON.stringify({ metrics: metricsToReport })
        );
      } else {
        // Use fetch for regular reporting
        await fetch(this.reportingEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ metrics: metricsToReport }),
        });
      }
    } catch (error) {
      console.warn('Failed to report performance metrics:', error);
      // Re-add metrics to queue for next attempt
      this.metrics.unshift(...metricsToReport);
    }
  }

  // Public API for custom metrics
  public recordCustomMetric(name: string, value: number, metadata?: any) {
    this.recordMetric(`CUSTOM_${name}`, value, metadata);
  }

  // Measure function execution time
  public measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    this.recordMetric(`FUNCTION_${name}`, duration);
    return result;
  }

  // Measure async function execution time
  public async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    this.recordMetric(`ASYNC_FUNCTION_${name}`, duration);
    return result;
  }

  // Get current metrics
  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // Clear metrics
  public clearMetrics() {
    this.metrics = [];
  }

  // Enable/disable monitoring
  public setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export function usePerformanceMonitoring() {
  const recordMetric = (name: string, value: number, metadata?: any) => {
    performanceMonitor.recordCustomMetric(name, value, metadata);
  };

  const measureRender = (componentName: string) => {
    const start = performance.now();
    return () => {
      const duration = performance.now() - start;
      performanceMonitor.recordCustomMetric(`RENDER_${componentName}`, duration);
    };
  };

  return {
    recordMetric,
    measureRender,
    measureFunction: performanceMonitor.measureFunction.bind(performanceMonitor),
    measureAsyncFunction: performanceMonitor.measureAsyncFunction.bind(performanceMonitor),
  };
}

// Higher-order component for measuring component render time
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const name = componentName || Component.displayName || Component.name || 'Unknown';

  return function PerformanceMonitoredComponent(props: P) {
    const { measureRender } = usePerformanceMonitoring();

    React.useEffect(() => {
      const endMeasure = measureRender(name);
      return endMeasure;
    });

    return React.createElement(Component, props);
  };
}
