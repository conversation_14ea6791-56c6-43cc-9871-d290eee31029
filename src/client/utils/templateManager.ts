import { ResumeTemplate } from '../../shared/types';
import { getTemplateById } from '../data/resumeTemplates';

/**
 * Template Manager - Handles template isolation and caching
 * Ensures each resume maintains its own template independently
 */
class TemplateManager {
  private templateCache: Map<string, ResumeTemplate> = new Map();
  private resumeTemplateMap: Map<string, string> = new Map(); // resumeId -> templateId

  /**
   * Get template for a specific resume
   */
  getTemplateForResume(resumeId: string, templateId?: string): ResumeTemplate | null {
    // If templateId is provided, use it and cache the mapping
    if (templateId) {
      this.resumeTemplateMap.set(resumeId, templateId);
      return this.getTemplate(templateId);
    }

    // Check if we have a cached mapping for this resume
    const cachedTemplateId = this.resumeTemplateMap.get(resumeId);
    if (cachedTemplateId) {
      return this.getTemplate(cachedTemplateId);
    }

    return null;
  }

  /**
   * Set template for a specific resume
   */
  setTemplateForResume(resumeId: string, templateId: string): ResumeTemplate | null {
    this.resumeTemplateMap.set(resumeId, templateId);
    return this.getTemplate(templateId);
  }

  /**
   * Get template by ID with caching
   */
  private getTemplate(templateId: string): ResumeTemplate | null {
    // Check cache first
    if (this.templateCache.has(templateId)) {
      return this.templateCache.get(templateId)!;
    }

    // Load template and cache it
    const template = getTemplateById(templateId);
    if (template) {
      this.templateCache.set(templateId, template);
      return template;
    }

    return null;
  }

  /**
   * Remove template mapping for a resume (when resume is deleted)
   */
  removeResumeTemplate(resumeId: string): void {
    this.resumeTemplateMap.delete(resumeId);
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this.templateCache.clear();
    this.resumeTemplateMap.clear();
  }

  /**
   * Get current template ID for a resume
   */
  getTemplateIdForResume(resumeId: string): string | null {
    return this.resumeTemplateMap.get(resumeId) || null;
  }

  /**
   * Check if a resume has a template assigned
   */
  hasTemplate(resumeId: string): boolean {
    return this.resumeTemplateMap.has(resumeId);
  }

  /**
   * Get all resume-template mappings (for debugging)
   */
  getAllMappings(): Record<string, string> {
    return Object.fromEntries(this.resumeTemplateMap);
  }

  /**
   * Validate template exists and is valid
   */
  validateTemplate(templateId: string): boolean {
    const template = this.getTemplate(templateId);
    if (!template || !template.styles) {
      return false;
    }

    const styles = template.styles;

    // Check required properties
    const hasRequired = styles.primaryColor &&
                       styles.fontFamily &&
                       styles.layout;

    // Validate color format (hex colors)
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    const validPrimaryColor = colorRegex.test(styles.primaryColor);
    const validSecondaryColor = !styles.secondaryColor || colorRegex.test(styles.secondaryColor);
    const validAccentColor = !styles.accentColor || colorRegex.test(styles.accentColor);

    // Validate layout
    const validLayout = ['single-column', 'two-column', 'sidebar'].includes(styles.layout);

    // Validate optional enum properties
    const validHeaderStyle = !styles.headerStyle ||
                             ['underline', 'background', 'border', 'minimal'].includes(styles.headerStyle);
    const validSkillsStyle = !styles.skillsStyle ||
                            ['tags', 'list', 'grid', 'inline'].includes(styles.skillsStyle);
    const validDateStyle = !styles.dateStyle ||
                          ['right', 'left', 'inline', 'separate-line'].includes(styles.dateStyle);

    return hasRequired &&
           validPrimaryColor &&
           validSecondaryColor &&
           validAccentColor &&
           validLayout &&
           validHeaderStyle &&
           validSkillsStyle &&
           validDateStyle;
  }

  /**
   * Get template with fallback to default
   */
  getTemplateWithFallback(templateId?: string): ResumeTemplate {
    if (templateId) {
      const template = this.getTemplate(templateId);
      if (template && this.validateTemplate(templateId)) {
        return template;
      }
    }

    // Return default template with comprehensive styling
    return {
      id: 'default',
      name: 'Default Template',
      description: 'Default resume template with professional styling',
      category: 'professional',
      preview: '',
      sampleData: {
        personalInfo: {
          fullName: '',
          email: '',
          phone: '',
          location: ''
        },
        summary: '',
        experience: [],
        education: [],
        skills: [],
        certifications: []
      },
      styles: {
        // Colors
        primaryColor: '#2D3748',
        secondaryColor: '#64748b',
        textColor: '#1e293b',
        backgroundColor: '#ffffff',

        // Typography
        fontFamily: 'Arial, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        headerFontFamily: 'Arial, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
        fontSize: '11px',
        lineHeight: '1.5',
        fontWeight: '400',
        headerFontWeight: '600',

        // Layout
        layout: 'single-column',
        spacing: '16px',
        sectionSpacing: '24px',
        padding: '32px',

        // Visual Elements
        borderStyle: 'solid',
        borderWidth: '1px',
        borderRadius: '0px',

        // Section Styling
        headerStyle: 'underline',
        skillsStyle: 'tags',
        dateStyle: 'right'
      }
    };
  }
}

// Export singleton instance
export const templateManager = new TemplateManager();

/**
 * Hook for React components to use template manager
 */
export const useTemplateManager = () => {
  return {
    getTemplateForResume: templateManager.getTemplateForResume.bind(templateManager),
    setTemplateForResume: templateManager.setTemplateForResume.bind(templateManager),
    removeResumeTemplate: templateManager.removeResumeTemplate.bind(templateManager),
    getTemplateIdForResume: templateManager.getTemplateIdForResume.bind(templateManager),
    hasTemplate: templateManager.hasTemplate.bind(templateManager),
    validateTemplate: templateManager.validateTemplate.bind(templateManager),
    getTemplateWithFallback: templateManager.getTemplateWithFallback.bind(templateManager),
    clearCache: templateManager.clearCache.bind(templateManager)
  };
};
