// Application Constants
// Centralized location for all application constants

// API Endpoints
export const API_ENDPOINTS = {
  OPENAI: 'https://api.openai.com/v1/chat/completions',
  STRIPE_WEBHOOK: '/stripe-webhook',
  PERFORMANCE: '/api/performance',
  LN_LOGIN: '/ln-login',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'careergpt-theme',
  SIDEBAR_COLLAPSED: 'careergpt-sidebar-collapsed',
  USER_PREFERENCES: 'careergpt-user-preferences',
  DRAFT_COVER_LETTER: 'careergpt-draft-cover-letter',
  FORM_AUTOSAVE: 'careergpt-form-autosave',
} as const;

// Application Limits
export const LIMITS = {
  COVER_LETTER_MAX_LENGTH: 5000,
  JOB_DESCRIPTION_MAX_LENGTH: 10000,
  RESUME_MAX_SIZE: 5 * 1024 * 1024, // 5MB
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 20,
  PASSWORD_MIN_LENGTH: 8,
  BIO_MAX_LENGTH: 500,
  COMPANY_NAME_MAX_LENGTH: 100,
  JOB_TITLE_MAX_LENGTH: 100,
  LOCATION_MAX_LENGTH: 100,
} as const;

// File Types
export const ALLOWED_FILE_TYPES = {
  RESUME: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  DOCUMENTS: ['application/pdf', 'text/plain', 'application/msword'],
} as const;

// GPT Models
export const GPT_MODELS = {
  GPT_4O_MINI: 'gpt-4o-mini',
  GPT_4O: 'gpt-4o',
  GPT_4: 'gpt-4',
} as const;

// Temperature Settings
export const TEMPERATURE_SETTINGS = {
  CREATIVE: 0.8,
  BALANCED: 0.5,
  FOCUSED: 0.2,
  MIN: 0,
  MAX: 1,
} as const;

// Subscription Status
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  PAST_DUE: 'past_due',
  CANCELED: 'canceled',
  INCOMPLETE: 'incomplete',
  INCOMPLETE_EXPIRED: 'incomplete_expired',
  TRIALING: 'trialing',
  UNPAID: 'unpaid',
} as const;

// Job Application Status
export const JOB_APPLICATION_STATUS = {
  APPLIED: 'Applied',
  INTERVIEW_SCHEDULED: 'Interview Scheduled',
  INTERVIEWED: 'Interviewed',
  OFFER_RECEIVED: 'Offer Received',
  REJECTED: 'Rejected',
  WITHDRAWN: 'Withdrawn',
  ON_HOLD: 'On Hold',
} as const;

// Interview Question Categories
export const INTERVIEW_CATEGORIES = {
  TECHNICAL: 'Technical',
  BEHAVIORAL: 'Behavioral',
  SITUATIONAL: 'Situational',
  COMPANY_SPECIFIC: 'Company Specific',
  GENERAL: 'General',
} as const;

// Badge Categories
export const BADGE_CATEGORIES = {
  LEARNING: 'learning',
  ACHIEVEMENT: 'achievement',
  MILESTONE: 'milestone',
  SPECIAL: 'special',
} as const;

// Learning Resource Types
export const LEARNING_RESOURCE_TYPES = {
  VIDEO: 'video',
  ARTICLE: 'article',
  COURSE: 'course',
  TUTORIAL: 'tutorial',
  WEBINAR: 'webinar',
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const;

// Animation Durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000,
} as const;

// Breakpoints (matching Chakra UI)
export const BREAKPOINTS = {
  BASE: 'base',
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
  XL: 'xl',
  '2XL': '2xl',
} as const;

// Z-Index Layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  GENERIC: 'Something went wrong. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  FILE_TOO_LARGE: 'File size is too large.',
  INVALID_FILE_TYPE: 'Invalid file type.',
  RATE_LIMIT: 'Too many requests. Please try again later.',
  PAYMENT_REQUIRED: 'Payment required to continue.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVED: 'Successfully saved!',
  UPDATED: 'Successfully updated!',
  DELETED: 'Successfully deleted!',
  CREATED: 'Successfully created!',
  UPLOADED: 'Successfully uploaded!',
  COPIED: 'Copied to clipboard!',
  EMAIL_SENT: 'Email sent successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
} as const;

// Regular Expressions
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  USERNAME: /^[a-zA-Z0-9_]+$/,
  URL: /^https?:\/\/.+/,
  LINKEDIN_JOB_URL: /linkedin\.com\/jobs\/view\/\d+/,
  STRONG_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
} as const;

// Date Formats
export const DATE_FORMATS = {
  SHORT: 'MM/dd/yyyy',
  LONG: 'MMMM dd, yyyy',
  ISO: 'yyyy-MM-dd',
  TIME: 'HH:mm',
  DATETIME: 'MM/dd/yyyy HH:mm',
  RELATIVE: 'relative', // for libraries like date-fns
} as const;

// Performance Thresholds
export const PERFORMANCE_THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  FID: { good: 100, poor: 300 },
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 },
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: process.env.NODE_ENV === 'production',
  ENABLE_ERROR_REPORTING: process.env.NODE_ENV === 'production',
  ENABLE_PERFORMANCE_MONITORING: true,
  ENABLE_BETA_FEATURES: process.env.NODE_ENV === 'development',
  ENABLE_DEBUG_MODE: process.env.NODE_ENV === 'development',
} as const;

// Social Media URLs
export const SOCIAL_URLS = {
  TWITTER: 'https://twitter.com/careergpt',
  LINKEDIN: 'https://linkedin.com/company/careergpt',
  GITHUB: 'https://github.com/careergpt',
  DISCORD: 'https://discord.gg/careergpt',
} as const;

// Support URLs
export const SUPPORT_URLS = {
  HELP_CENTER: '/help',
  CONTACT: '/contact',
  FAQ: '/faq',
  PRIVACY_POLICY: '/privacy',
  TERMS_OF_SERVICE: '/terms',
  BUG_REPORT: 'mailto:<EMAIL>',
} as const;

// Default Values
export const DEFAULT_VALUES = {
  CREATIVITY_LEVEL: 50,
  GPT_MODEL: GPT_MODELS.GPT_4O_MINI,
  TEMPERATURE: TEMPERATURE_SETTINGS.BALANCED,
  PAGINATION_SIZE: 10,
  DEBOUNCE_DELAY: 300,
  TOAST_DURATION: 5000,
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
} as const;

// Color Schemes
export const COLOR_SCHEMES = {
  PRIMARY: 'blue',
  SECONDARY: 'gray',
  SUCCESS: 'green',
  WARNING: 'yellow',
  ERROR: 'red',
  INFO: 'cyan',
} as const;

// Component Sizes
export const COMPONENT_SIZES = {
  XS: 'xs',
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
  XL: 'xl',
} as const;

// Export all constants as a single object for convenience
export const CONSTANTS = {
  API_ENDPOINTS,
  STORAGE_KEYS,
  LIMITS,
  ALLOWED_FILE_TYPES,
  GPT_MODELS,
  TEMPERATURE_SETTINGS,
  SUBSCRIPTION_STATUS,
  JOB_APPLICATION_STATUS,
  INTERVIEW_CATEGORIES,
  BADGE_CATEGORIES,
  LEARNING_RESOURCE_TYPES,
  NOTIFICATION_TYPES,
  ANIMATION_DURATION,
  BREAKPOINTS,
  Z_INDEX,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  REGEX_PATTERNS,
  DATE_FORMATS,
  PERFORMANCE_THRESHOLDS,
  FEATURE_FLAGS,
  SOCIAL_URLS,
  SUPPORT_URLS,
  DEFAULT_VALUES,
  COLOR_SCHEMES,
  COMPONENT_SIZES,
} as const;
