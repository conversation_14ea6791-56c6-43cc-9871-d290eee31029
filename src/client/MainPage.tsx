import { type User, type LnPayment } from "wasp/entities";
import { useAuth } from "wasp/client/auth";

import {
  generateCoverLetter,
  createJob,
  updateCoverLetter,
  updateLnPayment,
  importJobFromLinkedIn,
  useQuery,
  getJob,
  getCoverLetterCount,
} from "wasp/client/operations";

import {
  Box,
  HStack,
  VStack,
  Heading,
  Text,
  FormErrorMessage,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Button,
  FormHelperText,
  Code,
  Spinner,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  RadioGroup,
  Radio,
  Tooltip,
  useDisclosure,
  Container,
  Divider,
  SimpleGrid,
  Badge,
  useToast,
  Flex,
  Grid,
  GridItem,
  Card,
  CardBody,
  CardHeader,
  Stack,
  useColorModeValue,
  Avatar,
  AvatarGroup,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  useBreakpointValue,
  Image,
  AspectRatio,
  Center,
  Wrap,
  WrapItem,
  IconButton,
  Link,
  List,
  ListItem,
  ListIcon,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
} from '@chakra-ui/react';
import { motion, useScroll, useTransform } from 'framer-motion';
import BorderBox from './components/BorderBox';
import { LeaveATip, LoginToBegin } from './components/AlertDialog';
import { convertToSliderValue, convertToSliderLabel } from './components/CreativitySlider';
import HowItWorks from './components/HowItWorks';
import JobSuggestions from './components/JobSuggestions';
import * as pdfjsLib from 'pdfjs-dist';
import { useState, useEffect, useRef } from 'react';
import { ChangeEvent } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import LnPaymentModal from './components/LnPaymentModal';
import { fetchLightningInvoice } from './lightningUtils';
import type { LightningInvoice } from './lightningUtils';
import {
  FaFileAlt,
  FaPaperPlane,
  FaEnvelopeOpenText,
  FaBriefcase,
  FaComments,
  FaChartLine,
  FaLinkedin,
  FaRocket,
  FaUsers,
  FaShieldAlt,
  FaCheckCircle,
  FaStar,
  FaArrowRight,
  FaPlay,
  FaQuoteLeft,
  FaGraduationCap,
  FaLightbulb,
  FaTrophy,
  FaHeart,
  FaGithub,
  FaTwitter,
  FaGoogle,
  FaMicrosoft,
  FaAmazon,
  FaApple,
  FaFacebook,
  FaDropbox,
  FaStripe,
  FaBolt,
  FaBullseye,
  FaGlobe,
  FaClock,
  FaDownload,
  FaEdit,
  FaEye,
  FaThumbsUp,
  FaHandshake,
  FaMagic,
  FaRobot,
  FaBrain,
  FaChartBar,
  FaUserCheck,
  FaAward,
  FaGem,
  FaFire,
  FaUser,
  FaCog
} from 'react-icons/fa';
import { Icon } from '@chakra-ui/react';
import Logo from './components/Logo';
import LinkedInJobImport from './components/LinkedInJobImport';
import ResumeSelector from './components/ResumeSelector';
import UndrawIllustration, { IllustrationPresets } from './components/UndrawIllustration';
import UpgradeModal from './components/UpgradeModal';
import CreditsIndicator from './components/CreditsIndicator';

// Motion components
const MotionBox = motion(Box);
const MotionCard = motion(Card);
const MotionText = motion(Text);
const MotionHeading = motion(Heading);
const MotionButton = motion(Button);
const MotionFlex = motion(Flex);
const MotionGrid = motion(Grid);
const MotionVStack = motion(VStack);

// Define the job data structure
type JobData = {
  title: string;
  company: string;
  location: string;
  description: string;
};

// Data for the landing page
const features = [
  {
    icon: FaRocket,
    title: "AI-Powered Resume Builder",
    description: "Create professional resumes in minutes with our intelligent AI that adapts to your industry and role.",
    color: "blue.500"
  },
  {
    icon: FaEnvelopeOpenText,
    title: "Smart Cover Letters",
    description: "Generate personalized cover letters that perfectly match job descriptions and showcase your strengths.",
    color: "purple.500"
  },
  {
    icon: FaComments,
    title: "Interview Preparation",
    description: "Practice with AI-generated questions tailored to your target role and get instant feedback.",
    color: "green.500"
  },
  {
    icon: FaBriefcase,
    title: "Job Application Tracker",
    description: "Organize your job search with our comprehensive tracking system and never miss an opportunity.",
    color: "orange.500"
  },
  {
    icon: FaChartLine,
    title: "Career Analytics",
    description: "Get insights into your job search performance and optimize your strategy for better results.",
    color: "teal.500"
  },
  {
    icon: FaGraduationCap,
    title: "Learning Center",
    description: "Access curated career development resources and courses to advance your professional skills.",
    color: "pink.500"
  }
];

const testimonials = [
  {
    name: "Sarah Chen",
    role: "Software Engineer",
    company: "Google",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
    content: "CareerDart helped me land my dream job at Google! The AI-generated cover letters were spot-on and the interview prep was invaluable.",
    rating: 5
  },
  {
    name: "Marcus Johnson",
    role: "Product Manager",
    company: "Microsoft",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",
    content: "The resume builder is incredible. I went from 2% response rate to 40% after using CareerDart. Absolutely game-changing!",
    rating: 5
  },
  {
    name: "Emily Rodriguez",
    role: "UX Designer",
    company: "Apple",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",
    content: "I love how CareerDart tracks all my applications and provides insights. It made my job search so much more organized and effective.",
    rating: 5
  }
];

const stats = [
  { label: "Job Seekers Helped", value: "50,000+", icon: FaUsers },
  { label: "Success Rate", value: "89%", icon: FaTrophy },
  { label: "Average Time to Hire", value: "3.2 weeks", icon: FaClock },
  { label: "Cover Letters Generated", value: "250,000+", icon: FaFileAlt }
];

const companies = [
  { name: "Google", icon: FaGoogle },
  { name: "Microsoft", icon: FaMicrosoft },
  { name: "Apple", icon: FaApple },
  { name: "Amazon", icon: FaAmazon },
  { name: "Facebook", icon: FaFacebook },
  { name: "Dropbox", icon: FaDropbox }
];

function MainPage() {
  const [jobToFetch, setJobToFetch] = useState<string>('');
  const [isCoverLetterUpdate, setIsCoverLetterUpdate] = useState<boolean>(false);
  const [isCompleteCoverLetter, setIsCompleteCoverLetter] = useState<boolean>(true);
  const [sliderValue, setSliderValue] = useState(30);
  const [showTooltip, setShowTooltip] = useState(false);
  const [lightningInvoice, setLightningInvoice] = useState<LightningInvoice | null>(null);
  const [showLinkedInImport, setShowLinkedInImport] = useState<boolean>(false);
  const [resumeContent, setResumeContent] = useState<string>('');
  const [showCoverLetterForm, setShowCoverLetterForm] = useState(false);

  const toast = useToast();
  const { data: user } = useAuth();

  const navigate = useNavigate();
  const urlParams = new URLSearchParams(window.location.search);
  const jobIdParam = urlParams.get('job');

  // Color mode values
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const accentColor = useColorModeValue('blue.600', 'blue.400');

  const {
    data: job,
    isLoading: isJobLoading,
    error: getJobError,
  } = useQuery(getJob, { id: jobToFetch }, { enabled: jobToFetch.length > 0 });

  const { data: coverLetterCount } = useQuery(getCoverLetterCount, undefined, { enabled: !!user });

  const {
    handleSubmit,
    register,
    setValue,
    reset,
    clearErrors,
    trigger,
    watch,
    formState: { errors: formErrors, isSubmitting },
  } = useForm({
    defaultValues: {
      title: '',
      company: '',
      location: '',
      description: '',
      pdf: ''
    },
    mode: 'onChange'
  });

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: loginIsOpen, onOpen: loginOnOpen, onClose: loginOnClose } = useDisclosure();
  const { isOpen: lnPaymentIsOpen, onOpen: lnPaymentOnOpen, onClose: lnPaymentOnClose } = useDisclosure();
  const { isOpen: upgradeIsOpen, onOpen: upgradeOnOpen, onClose: upgradeOnClose } = useDisclosure();



  let setLoadingTextTimeout: ReturnType<typeof setTimeout>;
  const loadingTextRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (jobIdParam) {
      setJobToFetch(jobIdParam);
      setIsCoverLetterUpdate(true);
      resetJob();
    } else {
      setIsCoverLetterUpdate(false);
      // Only reset the form if we're not currently showing the LinkedIn import
      // This prevents the form from being cleared when LinkedIn import is active
      if (!showLinkedInImport) {
        reset({
          title: '',
          company: '',
          location: '',
          description: '',
          pdf: resumeContent || ''
        });
      }
    }
  }, [jobIdParam, job]);

  useEffect(() => {
    resetJob();
  }, [job]);

  function resetJob() {
    if (job) {
      reset({
        title: job.title,
        company: job.company,
        location: job.location,
        description: job.description,
        pdf: resumeContent || ''
      });
    }
  }

  // Resume content is now handled by the ResumeSelector component

  async function checkIfLnAndPay(user: Omit<User, 'password'>): Promise<LnPayment | null> {
    try {
      if (user.isUsingLn && user.credits === 0) {
        const invoice = await fetchLightningInvoice();
        let lnPayment: LnPayment;
        if (invoice) {
          invoice.status = 'pending';
          lnPayment = await updateLnPayment(invoice);
          setLightningInvoice(invoice);
          lnPaymentOnOpen();
        } else {
          throw new Error('fetching lightning invoice failed');
        }

        let status = invoice.status;
        while (status === 'pending') {
          lnPayment = await updateLnPayment(invoice);
          status = lnPayment.status;
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
        if (status !== 'success') {
          throw new Error('payment failed');
        }
        return lnPayment;
      }
    } catch (error) {
      console.error('Error processing payment, please try again');
    }
    return null;
  }

  function checkIfSubPastDueAndRedirect(user: Omit<User, 'password'>) {
    if (user.subscriptionStatus === 'past_due') {
      navigate('/profile')
      return true;
    } else {
      return false;
    }
  }

  async function onSubmit(values: any): Promise<void> {
    let canUserContinue = hasUserPaidOrActiveTrial();
    if (!user) {
      navigate('/login');
      return;
    }
    if (!canUserContinue) {
      upgradeOnOpen();
      return;
    }

    try {
      const lnPayment = await checkIfLnAndPay(user);

      const isSubscriptionPastDue = checkIfSubPastDueAndRedirect(user);
      if (isSubscriptionPastDue) return;

      const job = await createJob(values);

      const creativityValue = convertToSliderValue(sliderValue);

      const payload = {
        jobId: job.id,
        title: job.title,
        content: resumeContent || values.pdf,
        description: job.description,
        isCompleteCoverLetter,

        temperature: creativityValue,
        gptModel: values.gptModel || 'gpt-4o-mini',
        lnPayment: lnPayment || undefined,
      };

      setLoadingText();

      const coverLetter = await generateCoverLetter(payload);

      navigate(`/cover-letters/${coverLetter.id}`);
    } catch (error: any) {
      cancelLoadingText();
      alert(`${error?.message ?? 'Something went wrong, please try again'}`);
      console.error(error);
    }
  }

  async function onUpdate(values: any): Promise<void> {
    const canUserContinue = hasUserPaidOrActiveTrial();
    if (!user) {
      navigate('/login');
      return;
    }
    if (!canUserContinue) {
      upgradeOnOpen();
      return;
    }

    try {
      const lnPayment = await checkIfLnAndPay(user);

      const isSubscriptionPastDue = checkIfSubPastDueAndRedirect(user);
      if (isSubscriptionPastDue) return;

      if (!job) {
        throw new Error('Job not found');
      }

      const creativityValue = convertToSliderValue(sliderValue);
      const payload = {
        id: job.id,
        description: values.description,
        content: resumeContent || values.pdf,
        isCompleteCoverLetter,
        temperature: creativityValue,

        gptModel: values.gptModel || 'gpt-4o-mini',
        lnPayment: lnPayment || undefined,
      };

      setLoadingText();

      const coverLetterId = await updateCoverLetter(payload);

      navigate(`/cover-letters/${coverLetterId}`);
    } catch (error: any) {
      cancelLoadingText();
      alert(`${error?.message ?? 'Something went wrong, please try again'}`);
      console.error(error);
    }
  }

  // File button click is now handled by the ResumeSelector component

  function setLoadingText() {
    setLoadingTextTimeout = setTimeout(() => {
      loadingTextRef.current && (loadingTextRef.current.innerText = ' patience, my friend 🧘...');
    }, 2000);
  }

  function cancelLoadingText() {
    clearTimeout(setLoadingTextTimeout);
    loadingTextRef.current && (loadingTextRef.current.innerText = '');
  }

  async function handleLinkedInJobImport(jobData: JobData) {
    try {
      // Create a new form state with the imported data
      const formData = {
        title: jobData.title || '',
        company: jobData.company || '',
        location: jobData.location || '',
        description: jobData.description || '',
        pdf: resumeContent || '' // Keep existing resume content
      };

      // Reset the form with the new values - this should update all fields
      reset(formData);

      // Use setTimeout to ensure the reset has completed before setting individual values
      setTimeout(() => {
        // Set values individually to ensure they're updated
        setValue('title', formData.title, { shouldValidate: true, shouldDirty: true });
        setValue('company', formData.company, { shouldValidate: true, shouldDirty: true });
        setValue('location', formData.location, { shouldValidate: true, shouldDirty: true });
        setValue('description', formData.description, { shouldValidate: true, shouldDirty: true });

        // Trigger validation to update the form state
        trigger(['title', 'company', 'location', 'description']);
      }, 100);

      // Clear any form errors
      clearErrors(['title', 'company', 'location', 'description']);

      // Hide the LinkedIn import form
      setShowLinkedInImport(false);

      // Show success message
      toast({
        title: 'Job data imported',
        description: `Successfully imported job details for ${jobData.title} at ${jobData.company}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error handling LinkedIn job import:', error);
      toast({
        title: 'Import failed',
        description: 'Failed to import job data. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }

  function hasUserPaidOrActiveTrial(): Boolean {
    if (user) {
      if (user.isUsingLn) {
        if (user.credits < 3 && user.credits > 0) {
          onOpen();
        }
        return true;
      }
      if (!user.hasPaid && !user.isUsingLn && user.credits > 0) {
        if (user.credits < 3) {
          onOpen();
        }
        return user.credits > 0;
      }
      if (user.hasPaid) {
        return true;
      } else if (!user.hasPaid) {
        return false;
      }
    }
    return false;
  }

  // Job state is handled through conditional rendering in the JSX

  const handleResumeClick = () => {
    navigate('/resume');
  };

  const handleResumeSelected = (content: string) => {
    setResumeContent(content);
    setValue('pdf', content);
    console.log('Resume content set:', content.substring(0, 100) + '...');
  };



  // If user is logged in and wants to create a cover letter, show the form
  if (user && showCoverLetterForm) {
    return (
      <VStack spacing={{ base: 4, md: 6 }} align="stretch" px={{ base: 0, md: 0 }}>
        {/* Cover Letter Form */}
        <Container maxW="container.2xl" px={{ base: 0, md: 0 }}>
          <VStack spacing={{ base: 6, md: 8 }} align="stretch">
            <BorderBox
              position="relative"
              overflow="hidden"
              bg="white"
              borderRadius="2xl"
              boxShadow="xl"
              _before={{
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                bgGradient: 'gradient-primary',
                zIndex: 1,
              }}
            >
            <form
              onSubmit={!isCoverLetterUpdate ? handleSubmit(onSubmit) : handleSubmit(onUpdate)}
              style={{ width: '100%' }}
            >
              <VStack spacing={6} p={6}>
                <Box w="full" borderBottom="1px" borderColor="border-contrast-sm" pb={4}>
                  <HStack w="full" justify="space-between" align="center">
                    <VStack align="start" spacing={1}>
                      <Heading
                        size="md"
                        color="text-contrast-lg"
                        display="flex"
                        alignItems="center"
                        gap={2}
                      >
                        Job Information
                        {isCoverLetterUpdate && (
                          <Badge
                            colorScheme="purple"
                            px={3}
                            py={1}
                            borderRadius="full"
                            fontSize="xs"
                          >
                            Editing
                          </Badge>
                        )}
                      </Heading>
                      <Text fontSize="sm" color="text-contrast-md">
                        Fill in the details to generate your cover letter
                      </Text>
                    </VStack>
                    <HStack spacing={3}>
                      {user && (
                        <CreditsIndicator
                          credits={user.credits}
                          hasPaid={user.hasPaid}
                          isUsingLn={user.isUsingLn}
                          size="sm"
                        />
                      )}
                      <Button
                        leftIcon={<Icon as={FaLinkedin} />}
                        size="sm"
                        variant="outline"
                        colorScheme="blue"
                        onClick={() => setShowLinkedInImport(!showLinkedInImport)}
                        isDisabled={isCoverLetterUpdate}
                      >
                        Import from LinkedIn
                      </Button>
                      <Badge
                        colorScheme="blue"
                        px={3}
                        py={1}
                        borderRadius="full"
                        fontSize="sm"
                      >
                        {coverLetterCount?.toLocaleString()} Generated
                      </Badge>
                    </HStack>
                  </HStack>
                </Box>

                {showLinkedInImport && !isCoverLetterUpdate && (
                  <Box w="full" mb={4}>
                    <LinkedInJobImport
                      onJobImported={handleLinkedInJobImport}
                      isCollapsible={false}
                      inForm={true}
                    />
                  </Box>
                )}



                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
                  <FormControl isInvalid={!!formErrors.title}>
                    <FormLabel htmlFor="title" fontWeight="medium" color="text-contrast-lg">Job Title</FormLabel>
                    <Input
                      id='title'
                      placeholder='e.g., Senior Software Engineer'
                      aria-describedby={formErrors.title ? "title-error" : undefined}
                      {...register('title', {
                        required: 'This is required',
                        minLength: {
                          value: 2,
                          message: 'Minimum length should be 2',
                        },
                      })}
                      onFocus={(e: any) => {
                        if (user === null) {
                          loginOnOpen();
                          e.target.blur();
                        }
                      }}
                      disabled={isCoverLetterUpdate}
                      size="lg"
                    />
                    <FormErrorMessage id="title-error">
                      {!!formErrors.title && formErrors.title.message?.toString()}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!formErrors.company}>
                    <FormLabel htmlFor="company" fontWeight="medium" color="text-contrast-lg">Company Name</FormLabel>
                    <Input
                      id='company'
                      placeholder='e.g., Tech Corp'
                      aria-describedby={formErrors.company ? "company-error" : undefined}
                      {...register('company', {
                        required: 'This is required',
                        minLength: {
                          value: 1,
                          message: 'Minimum length should be 1',
                        },
                      })}
                      disabled={isCoverLetterUpdate}
                      size="lg"
                    />
                    <FormErrorMessage id="company-error">
                      {!!formErrors.company && formErrors.company.message?.toString()}
                    </FormErrorMessage>
                  </FormControl>
                </SimpleGrid>

                <FormControl isInvalid={!!formErrors.location}>
                  <FormLabel fontWeight="medium" color="text-contrast-lg">Location</FormLabel>
                  <Input
                    id='location'
                    placeholder='e.g., San Francisco, CA or Remote'
                    {...register('location', {
                      required: 'This is required',
                      minLength: {
                        value: 2,
                        message: 'Minimum length should be 2',
                      },
                    })}
                    size="lg"
                  />
                  <FormErrorMessage>{!!formErrors.location && formErrors.location.message?.toString()}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!formErrors.description}>
                  <FormLabel fontWeight="medium" color="text-contrast-lg">Job Description</FormLabel>
                  <Textarea
                    id='description'
                    placeholder='Paste the job description here...'
                    {...register('description', {
                      required: 'This is required',
                    })}
                    size="lg"
                    minH="200px"
                  />
                  <FormErrorMessage>
                    {!!formErrors.description && formErrors.description.message?.toString()}
                  </FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!formErrors.pdf}>
                  <ResumeSelector onResumeSelected={handleResumeSelected} />
                  <Input
                    id='pdf'
                    type='hidden'
                    {...register('pdf', {
                      required: 'Please select or upload a resume',
                      validate: () => !!resumeContent || 'Please select or upload a resume'
                    })}
                    value={resumeContent}
                  />
                  <FormErrorMessage>{!!formErrors.pdf && formErrors.pdf.message?.toString()}</FormErrorMessage>
                  <HStack mt={2} justify="flex-end">
                    <Button
                      leftIcon={<Icon as={FaFileAlt} />}
                      onClick={handleResumeClick}
                      colorScheme="blue"
                      variant="outline"
                      size="sm"
                    >
                      Manage All Resumes
                    </Button>
                  </HStack>
                </FormControl>

                <VStack spacing={4} w="full" bg="bg-contrast-sm" p={4} borderRadius="xl">
                  <FormControl>
                    <FormLabel fontWeight="medium" color="text-contrast-lg">Creativity Level</FormLabel>
                    <Slider
                      id='temperature'
                      defaultValue={30}
                      min={0}
                      max={68}
                      colorScheme='purple'
                      onChange={(v) => setSliderValue(v)}
                      onMouseEnter={() => setShowTooltip(true)}
                      onMouseLeave={() => setShowTooltip(false)}
                    >
                      <SliderTrack>
                        <SliderFilledTrack />
                      </SliderTrack>
                      <Tooltip
                        hasArrow
                        bg='purple.500'
                        color='white'
                        placement='top'
                        isOpen={showTooltip}
                        label={`${convertToSliderLabel(sliderValue)}`}
                      >
                        <SliderThumb />
                      </Tooltip>
                    </Slider>
                    <HStack justify="space-between" mt={2}>
                      <Text fontSize="sm" color="text-contrast-md">More Professional</Text>
                      <Text fontSize="sm" color="text-contrast-md">More Creative</Text>
                    </HStack>
                  </FormControl>


                </VStack>

                <Button
                  colorScheme='blue'
                  size="lg"
                  w="full"
                  isLoading={isSubmitting}
                  disabled={user === null}
                  type='submit'
                  leftIcon={<Icon as={FaPaperPlane} />}
                >
                  {!isCoverLetterUpdate ? 'Generate Cover Letter' : 'Create New Cover Letter'}
                </Button>

                <Text fontSize="xs" color="gray.500" textAlign="center" mt={2}>
                  ⚠️ AI-generated content is for guidance only. Please review and customize before use.
                </Text>

                <Text fontSize="xs" color="gray.500" textAlign="center" mt={1}>
                  By using our service, you agree to our{' '}
                  <Link as={RouterLink} to="/tos" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                    Terms of Service
                  </Link>
                  {' '}and{' '}
                  <Link as={RouterLink} to="/privacy" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                    Privacy Policy
                  </Link>
                </Text>

                <Text ref={loadingTextRef} fontSize='sm' fontStyle='italic' color='text-contrast-md' textAlign="center">
                  {' '}
                </Text>
              </VStack>
            </form>
          </BorderBox>

            {/* Job Suggestions Section */}
            <JobSuggestions />
          </VStack>
        </Container>

        <LeaveATip
          isOpen={isOpen}
          onOpen={onOpen}
          onClose={onClose}
          credits={user?.credits || 0}
          isUsingLn={user?.isUsingLn || false}
        />
        <LoginToBegin isOpen={loginIsOpen} onOpen={loginOnOpen} onClose={loginOnClose} />
        <LnPaymentModal isOpen={lnPaymentIsOpen} onClose={lnPaymentOnClose} lightningInvoice={lightningInvoice} />
        <UpgradeModal
          isOpen={upgradeIsOpen}
          onClose={upgradeOnClose}
          userCredits={user?.credits || 0}
          feature="cover-letter"
        />
      </VStack>
    );
  }

  // Main Landing Page
  return (
    <Box minH="100vh" bg={bgGradient} position="relative" overflow="hidden">
      {/* Background Pattern */}
      <Box
        position="absolute"
        top="0"
        left="0"
        right="0"
        bottom="0"
        opacity={0.05}
        bgImage="radial-gradient(circle at 25px 25px, rgba(66, 153, 225, 0.3) 2px, transparent 0)"
        bgSize="50px 50px"
      />

      {/* Hero Section */}
      <Container maxW="7xl" pt={{ base: 8, md: 16 }} pb={{ base: 12, md: 20 }}>
        <MotionVStack
          spacing={{ base: 10, md: 16 }}
          textAlign="center"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Trust Badge */}
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Badge
              colorScheme="blue"
              variant="subtle"
              px={4}
              py={2}
              borderRadius="full"
              fontSize="sm"
              fontWeight="600"
              textTransform="none"
            >
              AI-Powered Career Intelligence
            </Badge>
          </MotionBox>

          {/* Hero Headline */}
          <VStack spacing={8} maxW="6xl">
            <MotionHeading
              as="h1"
              fontSize={{ base: "4xl", md: "6xl", lg: "7xl" }}
              fontWeight="black"
              lineHeight="shorter"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Text
                as="span"
                bgGradient="linear(to-r, blue.500, purple.500, pink.500)"
                bgClip="text"
              >
                Your Personal Career Companion
              </Text>
            </MotionHeading>

            <MotionText
              fontSize={{ base: "xl", md: "2xl" }}
              color={textColor}
              maxW="5xl"
              fontWeight="500"
              textAlign="center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              Experience personalized career development intelligence that adapts to your unique goals, skills, and industry—delivering custom strategies for your success.
            </MotionText>

            {/* Personalization Features */}
            <MotionGrid
              templateColumns={{ base: "1fr", md: "repeat(3, 1fr)" }}
              gap={8}
              w="full"
              maxW="5xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <VStack spacing={4} p={6} bg={cardBg} borderRadius="2xl" border="1px solid" borderColor={useColorModeValue('gray.200', 'gray.700')}>
                <Icon as={FaUser} boxSize={8} color="blue.500" />
                <VStack spacing={2}>
                  <Text fontWeight="700" fontSize="lg" color={textColor}>Personalized</Text>
                  <Text fontSize="sm" color="gray.500" textAlign="center">
                    AI learns your career goals, skills, and preferences to create tailored recommendations
                  </Text>
                </VStack>
              </VStack>

              <VStack spacing={4} p={6} bg={cardBg} borderRadius="2xl" border="1px solid" borderColor={useColorModeValue('gray.200', 'gray.700')}>
                <Icon as={FaCog} boxSize={8} color="purple.500" />
                <VStack spacing={2}>
                  <Text fontWeight="700" fontSize="lg" color={textColor}>Custom Built</Text>
                  <Text fontSize="sm" color="gray.500" textAlign="center">
                    Every resume, cover letter, and strategy is uniquely crafted for your target roles
                  </Text>
                </VStack>
              </VStack>

              <VStack spacing={4} p={6} bg={cardBg} borderRadius="2xl" border="1px solid" borderColor={useColorModeValue('gray.200', 'gray.700')}>
                <Icon as={FaHeart} boxSize={8} color="pink.500" />
                <VStack spacing={2}>
                  <Text fontWeight="700" fontSize="lg" color={textColor}>User-Focused</Text>
                  <Text fontSize="sm" color="gray.500" textAlign="center">
                    Designed around your success with intuitive tools that grow with your career
                  </Text>
                </VStack>
              </VStack>
            </MotionGrid>
          </VStack>

          {/* CTA Button */}
          <MotionBox
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <MotionButton
              size="xl"
              colorScheme="blue"
              px={12}
              py={8}
              fontSize="xl"
              fontWeight="700"
              borderRadius="2xl"
              h="auto"
              minH="16"
              onClick={() => user ? setShowCoverLetterForm(true) : navigate('/login')}
              _hover={{
                transform: 'translateY(-3px)',
                boxShadow: '0 20px 40px rgba(66, 153, 225, 0.4)',
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition="all 0.3s"
            >
              {user ? 'Start Your Journey' : 'Begin Your Career Transformation'}
            </MotionButton>
          </MotionBox>

          {/* Intelligence Showcase */}
          <MotionBox
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
            maxW="6xl"
            w="full"
          >
            <VStack spacing={8}>
              <VStack spacing={4}>
                <Heading size="xl" fontWeight="bold" color={textColor}>
                  Career Intelligence That Understands You
                </Heading>
                <Text fontSize="lg" color="gray.500" maxW="3xl" textAlign="center">
                  Our AI analyzes your background, aspirations, and market trends to deliver insights that accelerate your career growth
                </Text>
              </VStack>

              <Grid templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(4, 1fr)" }} gap={6} w="full">
                <VStack spacing={3} p={4}>
                  <Icon as={FaBrain} boxSize={10} color="blue.500" />
                  <Text fontSize="sm" fontWeight="600" color={textColor} textAlign="center">Smart Resume Optimization</Text>
                </VStack>
                <VStack spacing={3} p={4}>
                  <Icon as={FaRobot} boxSize={10} color="purple.500" />
                  <Text fontSize="sm" fontWeight="600" color={textColor} textAlign="center">AI Interview Coaching</Text>
                </VStack>
                <VStack spacing={3} p={4}>
                  <Icon as={FaChartLine} boxSize={10} color="green.500" />
                  <Text fontSize="sm" fontWeight="600" color={textColor} textAlign="center">Career Path Analytics</Text>
                </VStack>
                <VStack spacing={3} p={4}>
                  <Icon as={FaGem} boxSize={10} color="pink.500" />
                  <Text fontSize="sm" fontWeight="600" color={textColor} textAlign="center">Personalized Insights</Text>
                </VStack>
              </Grid>
            </VStack>
          </MotionBox>

          {/* Success Quote */}
          <MotionBox
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
            maxW="3xl"
          >
            <VStack spacing={4}>
              <Text
                fontSize={{ base: "lg", md: "xl" }}
                fontStyle="italic"
                color={textColor}
                textAlign="center"
                fontWeight="500"
              >
                "The personalized approach made all the difference. CareerDart understood my unique background and helped me craft a strategy that actually worked for my industry."
              </Text>
              <HStack spacing={1}>
                {[...Array(5)].map((_, i) => (
                  <Icon key={i} as={FaStar} color="yellow.400" boxSize={5} />
                ))}
              </HStack>
            </VStack>
          </MotionBox>
        </MotionVStack>
      </Container>

      {/* Company Logos Section */}
      <Container maxW="7xl" py={{ base: 8, md: 12 }}>
        <MotionVStack
          spacing={6}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <Text fontSize="lg" fontWeight="600" color="gray.500" textAlign="center">
            Trusted by job seekers who've landed at top companies
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Our users have secured positions at industry-leading companies such as
          </Text>

          {/* Animated Company Logos */}
          <Box w="full" overflow="hidden">
            <Flex
              animation="scroll 30s linear infinite"
              sx={{
                '@keyframes scroll': {
                  '0%': { transform: 'translateX(0)' },
                  '100%': { transform: 'translateX(-50%)' }
                }
              }}
            >
              {[...companies, ...companies].map((company, index) => (
                <Flex
                  key={index}
                  minW="200px"
                  h="80px"
                  align="center"
                  justify="center"
                  mx={4}
                >
                  <HStack spacing={3} opacity={0.6} _hover={{ opacity: 1 }} transition="opacity 0.3s">
                    <Icon as={company.icon} boxSize={8} color="gray.500" />
                    <Text fontSize="lg" fontWeight="600" color="gray.500">
                      {company.name}
                    </Text>
                  </HStack>
                </Flex>
              ))}
            </Flex>
          </Box>
        </MotionVStack>
      </Container>



      {/* Stats Section */}
      <Container maxW="7xl" py={{ base: 10, md: 16 }}>
        <MotionVStack
          spacing={12}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 2 }}
        >
          <VStack spacing={4} textAlign="center">
            <Heading size="2xl" fontWeight="black" color={textColor}>
              Intelligent Career Development That Delivers
            </Heading>
            <Text fontSize="xl" color="gray.500" maxW="3xl">
              Our personalized AI approach creates measurable results for professionals across all industries
            </Text>
          </VStack>

          <Grid templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(4, 1fr)" }} gap={8}>
            {stats.map((stat, index) => (
              <MotionCard
                key={index}
                bg={cardBg}
                borderRadius="2xl"
                p={6}
                textAlign="center"
                border="1px solid"
                borderColor={useColorModeValue('gray.200', 'gray.700')}
                _hover={{
                  transform: 'translateY(-4px)',
                  boxShadow: 'xl',
                }}
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 2.2 + index * 0.1 }}
              >
                <VStack spacing={3}>
                  <Icon as={stat.icon} boxSize={8} color={accentColor} />
                  <Stat>
                    <StatNumber fontSize="2xl" fontWeight="black" color={textColor}>
                      {stat.value}
                    </StatNumber>
                    <StatLabel fontSize="sm" color="gray.500">
                      {stat.label}
                    </StatLabel>
                  </Stat>
                </VStack>
              </MotionCard>
            ))}
          </Grid>

          <MotionButton
            size="lg"
            colorScheme="blue"
            px={8}
            py={6}
            fontSize="lg"
            fontWeight="600"
            borderRadius="xl"
            onClick={() => user ? setShowCoverLetterForm(true) : navigate('/login')}
            _hover={{
              transform: 'translateY(-2px)',
              boxShadow: 'xl',
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 2.6 }}
          >
            {user ? 'Start Your Journey' : 'Begin Your Career Transformation'}
          </MotionButton>
        </MotionVStack>
      </Container>

      {/* Features Section */}
      <Container maxW="7xl" py={{ base: 10, md: 16 }}>
        <MotionVStack
          spacing={12}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <VStack spacing={4} textAlign="center">
            <Heading size="2xl" fontWeight="black" color={textColor}>
              Everything You Need to Land Your Dream Job
            </Heading>
            <Text fontSize="xl" color="gray.500" maxW="3xl">
              Our comprehensive suite of AI-powered tools gives you the competitive edge
              in today's job market.
            </Text>
          </VStack>

          <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={8}>
            {features.map((feature, index) => (
              <MotionCard
                key={index}
                bg={cardBg}
                borderRadius="2xl"
                p={8}
                border="1px solid"
                borderColor={useColorModeValue('gray.200', 'gray.700')}
                _hover={{
                  transform: 'translateY(-4px)',
                  boxShadow: 'xl',
                }}
                whileHover={{ scale: 1.02 }}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
              >
                <VStack spacing={4} align="start">
                  <Icon as={feature.icon} boxSize={10} color={feature.color} />
                  <Heading size="md" fontWeight="bold" color={textColor}>
                    {feature.title}
                  </Heading>
                  <Text color="gray.500" lineHeight="tall">
                    {feature.description}
                  </Text>
                </VStack>
              </MotionCard>
            ))}
          </Grid>
        </MotionVStack>
      </Container>

      {/* Testimonials Section */}
      <Container maxW="7xl" py={{ base: 10, md: 16 }}>
        <MotionVStack
          spacing={12}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.6 }}
        >
          <VStack spacing={4} textAlign="center">
            <Heading size="2xl" fontWeight="black" color={textColor}>
              Success Stories from Our Community
            </Heading>
            <Text fontSize="xl" color="gray.500" maxW="3xl">
              See how CareerDart has transformed careers and helped professionals
              achieve their goals.
            </Text>
          </VStack>

          <Grid templateColumns={{ base: "1fr", md: "repeat(3, 1fr)" }} gap={8}>
            {testimonials.map((testimonial, index) => (
              <MotionCard
                key={index}
                bg={cardBg}
                borderRadius="2xl"
                p={8}
                border="1px solid"
                borderColor={useColorModeValue('gray.200', 'gray.700')}
                _hover={{
                  transform: 'translateY(-4px)',
                  boxShadow: 'xl',
                }}
                whileHover={{ scale: 1.02 }}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.8 + index * 0.1 }}
              >
                <VStack spacing={4} align="start">
                  <Icon as={FaQuoteLeft} boxSize={6} color={accentColor} opacity={0.6} />
                  <Text color={textColor} lineHeight="tall" fontStyle="italic">
                    "{testimonial.content}"
                  </Text>
                  <HStack spacing={3}>
                    <Avatar size="sm" src={testimonial.avatar} name={testimonial.name} />
                    <VStack spacing={0} align="start">
                      <Text fontWeight="bold" fontSize="sm" color={textColor}>
                        {testimonial.name}
                      </Text>
                      <Text fontSize="xs" color="gray.500">
                        {testimonial.role} at {testimonial.company}
                      </Text>
                    </VStack>
                  </HStack>
                  <HStack spacing={1}>
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Icon key={i} as={FaStar} color="yellow.400" boxSize={4} />
                    ))}
                  </HStack>
                </VStack>
              </MotionCard>
            ))}
          </Grid>
        </MotionVStack>
      </Container>



      {/* Modals */}
      <LeaveATip
        isOpen={isOpen}
        onOpen={onOpen}
        onClose={onClose}
        credits={user?.credits || 0}
        isUsingLn={user?.isUsingLn || false}
      />
      <LoginToBegin isOpen={loginIsOpen} onOpen={loginOnOpen} onClose={loginOnClose} />
      <LnPaymentModal isOpen={lnPaymentIsOpen} onClose={lnPaymentOnClose} lightningInvoice={lightningInvoice} />
      <UpgradeModal
        isOpen={upgradeIsOpen}
        onClose={upgradeOnClose}
        userCredits={user?.credits || 0}
        feature="cover-letter"
      />
    </Box>
  );
}

export default MainPage;