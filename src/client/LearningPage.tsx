import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
  Badge,
  Image,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Progress,
  Flex,
  Link,
  Icon,
  Divider,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  useToast
} from '@chakra-ui/react';
import {
  FaSearch,
  FaBookmark,
  FaRegBookmark,
  FaExternalLinkAlt,
  FaGraduationCap,
  FaCode,
  FaBriefcase,
  FaChartLine,
  FaLock,
  FaUnlock,
  FaCheck,
  FaFileAlt,
  FaPlay,
  FaUsers,
  FaUser
} from 'react-icons/fa';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ContentContainer from './components/ContentContainer';
import ActionButton from './components/ActionButton';
import VideoEmbed from './components/VideoEmbed';
import UndrawIllustration, { IllustrationPresets } from './components/UndrawIllustration';
import { updateLearningProgress, initializeBadges, useQuery, getLearningProgress } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';

// Learning resources data focused on career development and mastery
const learningResources = [
  // General Career Development Resources
  {
    id: 1,
    title: "Career Growth Mindset",
    description: "Develop the mindset and strategies needed for continuous career advancement and professional growth.",
    category: "General",
    subcategory: "Mindset",
    level: "Beginner",
    duration: "15 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/75GFzikmRY0",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: false
  },
  {
    id: 2,
    title: "Leadership Skills Development",
    description: "Essential leadership skills every professional needs to advance their career and influence others.",
    category: "General",
    subcategory: "Leadership",
    level: "Intermediate",
    duration: "18 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/HAnw168huqA",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: false
  },
  {
    id: 3,
    title: "Effective Communication at Work",
    description: "Master workplace communication skills to build better relationships and advance your career.",
    category: "General",
    subcategory: "Communication",
    level: "Beginner",
    duration: "12 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/eIho2S0ZahI",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: false
  },
  {
    id: 4,
    title: "Time Management Mastery",
    description: "Learn proven time management techniques to boost productivity and achieve work-life balance.",
    category: "General",
    subcategory: "Productivity",
    level: "Intermediate",
    duration: "10 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/iONDebHX9qk",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: false
  },
  {
    id: 5,
    title: "Building Professional Networks",
    description: "Strategic networking approaches to build meaningful professional relationships and opportunities.",
    category: "General",
    subcategory: "Networking",
    level: "Intermediate",
    duration: "14 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/jpe-LKn-4gM",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1515187029135-18ee286d815b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: false
  },
  {
    id: 6,
    title: "Salary Negotiation Mastery",
    description: "Advanced strategies for negotiating salary, benefits, and career advancement opportunities.",
    category: "General",
    subcategory: "Negotiation",
    level: "Advanced",
    duration: "16 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/km2Hd_xgo9Q",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1589939705384-5185137a7f0f?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: true
  },
  // Personalized Career Development Resources
  {
    id: 7,
    title: "Interview Confidence Building",
    description: "Personalized techniques to overcome interview anxiety and present your best self.",
    category: "Personalized",
    subcategory: "Interview Skills",
    level: "Intermediate",
    duration: "20 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/naIkpQ_cIt0",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: false
  },
  {
    id: 8,
    title: "Personal Branding Strategy",
    description: "Develop your unique professional brand and online presence to stand out in your field.",
    category: "Personalized",
    subcategory: "Personal Brand",
    level: "Advanced",
    duration: "22 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/bx6HTcQ8i2U",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1611944212129-29977ae1398c?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: true
  },
  {
    id: 9,
    title: "Career Transition Planning",
    description: "Strategic approach to changing careers or industries while leveraging your existing skills.",
    category: "Personalized",
    subcategory: "Career Change",
    level: "Advanced",
    duration: "25 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/YRhqMWUH2Ig",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: true
  },
  {
    id: 10,
    title: "Career Development Tips",
    description: "Short, actionable career advice for busy professionals looking to level up quickly.",
    category: "Personalized",
    subcategory: "Quick Tips",
    level: "Beginner",
    duration: "8 min",
    type: "video",
    videoUrl: "https://www.youtube.com/embed/9bZkp7q19f0",
    videoType: "youtube" as const,
    image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    progress: 0,
    completed: false,
    premium: false
  }
];

// Updated categories for career development focus
const categories = ["All", "General", "Personalized"];

export default function LearningPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [savedResources, setSavedResources] = useState<number[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [videoStartTime, setVideoStartTime] = useState<number>(0);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const { data: user } = useAuth();

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Fetch user's learning progress only if user is authenticated
  const { data: learningProgressData, isLoading: progressLoading } = useQuery(getLearningProgress, undefined, { enabled: !!user });

  // Create a map of resource progress for easy lookup
  const progressMap = React.useMemo(() => {
    if (!learningProgressData) return {};
    return learningProgressData.reduce((acc: any, progress: any) => {
      acc[progress.resourceId] = progress;
      return acc;
    }, {});
  }, [learningProgressData]);

  // Merge learning resources with user progress
  const resourcesWithProgress = React.useMemo(() => {
    return learningResources.map(resource => {
      const userProgress = progressMap[resource.id.toString()];
      return {
        ...resource,
        progress: userProgress?.progress || 0,
        completed: userProgress?.completed || false,
        timeSpent: userProgress?.timeSpent || 0,
        lastAccessed: userProgress?.lastAccessed
      };
    });
  }, [progressMap]);

  const filteredResources = resourcesWithProgress.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || resource.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleSaveResource = (id: number) => {
    if (savedResources.includes(id)) {
      setSavedResources(savedResources.filter(rId => rId !== id));
    } else {
      setSavedResources([...savedResources, id]);
    }
  };

  const handleVideoPlay = (resource: any) => {
    setSelectedVideo(resource);
    setVideoStartTime(Date.now());
    onOpen();

    // Track that user started watching
    updateProgress(resource.id, 10, false, 0);
  };

  const updateProgress = async (resourceId: number, progress: number, completed: boolean = false, timeSpent: number = 0) => {
    try {
      await updateLearningProgress({
        resourceId: resourceId.toString(),
        resourceType: 'video',
        progress,
        completed,
        timeSpent
      });
    } catch (error) {
      console.error('Failed to update learning progress:', error);
    }
  };

  const handleVideoComplete = (resource: any) => {
    const timeSpent = Math.floor((Date.now() - videoStartTime) / 1000);
    updateProgress(resource.id, 100, true, timeSpent);

    toast({
      title: "Video Completed!",
      description: `You've completed "${resource.title}". Great job!`,
      status: "success",
      duration: 3000,
      isClosable: true,
    });
  };

  const handleModalClose = () => {
    if (selectedVideo && videoStartTime > 0) {
      const timeSpent = Math.floor((Date.now() - videoStartTime) / 1000);
      // Update progress to 75% if they watched for more than 30 seconds
      if (timeSpent > 30) {
        updateProgress(selectedVideo.id, 75, false, timeSpent);
      }
    }
    onClose();
  };

  const handleInitializeBadges = async () => {
    try {
      const result = await initializeBadges();
      toast({
        title: "Badges Initialized!",
        description: result.message,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to initialize badges",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'General': return FaUsers;
      case 'Personalized': return FaUser;
      case 'Interview': return FaGraduationCap;
      case 'Career': return FaBriefcase;
      case 'Networking': return FaChartLine;
      default: return FaGraduationCap;
    }
  };

  return (
    <ContentPageBox>
      <VStack spacing={3} align="stretch">
        {/* Learning Center Header with Illustration */}
        <Flex
          direction={{ base: 'column', md: 'row' }}
          align={{ base: 'center', md: 'flex-start' }}
          justify="space-between"
          gap={4}
          py={2}
        >
          {/* Left side - Title, Description, and Buttons */}
          <VStack align={{ base: 'center', md: 'flex-start' }} spacing={3} flex={1}>
            <Heading
              size="xl"
              color="gray.700"
              _dark={{ color: 'gray.200' }}
              textAlign={{ base: 'center', md: 'left' }}
            >
              Learning Center
            </Heading>

            <Text
              color="gray.600"
              _dark={{ color: 'gray.400' }}
              fontSize="md"
              textAlign={{ base: 'center', md: 'left' }}
              lineHeight="1.6"
              maxW="md"
            >
              Enhance your career skills with curated learning resources and tutorials designed to accelerate your professional growth.
            </Text>

            <HStack spacing={3} mt={2}>
              <ActionButton
                icon={FaGraduationCap}
                label="My Learning"
                variant="outline"
                onClick={() => {}}
              />
              <ActionButton
                icon={FaCheck}
                label="Initialize Badges"
                variant="primary"
                onClick={handleInitializeBadges}
              />
            </HStack>
          </VStack>

          {/* Right side - Illustration */}
          <Box flexShrink={0}>
            <UndrawIllustration
              illustration="online_learning_re_qw08"
              size="sm"
              primaryColor="6366f1"
              py={0}
            />
          </Box>
        </Flex>

      <ContentContainer delay={0.3}>
        <HStack spacing={4} mb={4}>
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <Icon as={FaSearch} color="gray.400" />
            </InputLeftElement>
            <Input
              placeholder="Search resources..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              borderRadius="md"
            />
          </InputGroup>
        </HStack>

        <Tabs colorScheme="purple" variant="enclosed" isLazy>
          <TabList overflowX="auto" overflowY="hidden" whiteSpace="nowrap" pb={2}>
            {categories.map(category => (
              <Tab key={category} onClick={() => setSelectedCategory(category)}>
                {category}
              </Tab>
            ))}
            <Tab onClick={() => setSavedResources([])}>Saved ({savedResources.length})</Tab>
          </TabList>

          <TabPanels>
            {/* Render panels for each category + saved */}
            {[...categories, 'Saved'].map((category, index) => (
              <TabPanel key={index} p={0} pt={4}>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                  {(category === 'Saved'
                    ? learningResources.filter(r => savedResources.includes(r.id))
                    : filteredResources
                  ).map(resource => (
                    <Box
                      key={resource.id}
                      borderWidth="0"
                      borderRadius="xl"
                      overflow="hidden"
                      bg={cardBg}
                      boxShadow="sm"
                      transition="all 0.3s ease"
                      _hover={{
                        transform: 'translateY(-4px)',
                        boxShadow: 'lg',
                        _dark: { boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3)' }
                      }}
                    >
                      <Box position="relative">
                        {resource.type === 'video' ? (
                          <Box position="relative" height="150px">
                            <VideoEmbed
                              videoUrl={resource.videoUrl}
                              videoType={resource.videoType}
                              title={resource.title}
                              showPreview={true}
                              onPlay={() => handleVideoPlay(resource)}
                            />
                            <Button
                              position="absolute"
                              top="2"
                              right="2"
                              size="sm"
                              variant="ghost"
                              color={savedResources.includes(resource.id) ? "purple.500" : "white"}
                              onClick={() => toggleSaveResource(resource.id)}
                              zIndex={2}
                              borderRadius="full"
                              minW="auto"
                              h="auto"
                              p={2}
                              _hover={{
                                bg: savedResources.includes(resource.id) ? 'purple.100' : 'blackAlpha.200',
                                transform: 'scale(1.1)'
                              }}
                              transition="all 0.2s"
                            >
                              <Icon
                                as={savedResources.includes(resource.id) ? FaBookmark : FaRegBookmark}
                                boxSize={4}
                              />
                            </Button>
                            {resource.premium && (
                              <Badge
                                position="absolute"
                                top="2"
                                left="2"
                                colorScheme="purple"
                                variant="solid"
                                zIndex={2}
                              >
                                Premium
                              </Badge>
                            )}
                          </Box>
                        ) : (
                          <>
                            <Image
                              src={resource.image}
                              alt={resource.title}
                              height="150px"
                              width="100%"
                              objectFit="cover"
                            />
                            <Button
                              position="absolute"
                              top="2"
                              right="2"
                              size="sm"
                              variant="ghost"
                              color={savedResources.includes(resource.id) ? "purple.500" : "white"}
                              onClick={() => toggleSaveResource(resource.id)}
                              borderRadius="full"
                              minW="auto"
                              h="auto"
                              p={2}
                              _hover={{
                                bg: savedResources.includes(resource.id) ? 'purple.100' : 'blackAlpha.200',
                                transform: 'scale(1.1)'
                              }}
                              transition="all 0.2s"
                            >
                              <Icon
                                as={savedResources.includes(resource.id) ? FaBookmark : FaRegBookmark}
                                boxSize={4}
                              />
                            </Button>
                            {resource.premium && (
                              <Badge
                                position="absolute"
                                top="2"
                                left="2"
                                colorScheme="purple"
                                variant="solid"
                              >
                                Premium
                              </Badge>
                            )}
                          </>
                        )}
                      </Box>

                      <Box p={5}>
                        {/* Minimalistic badges */}
                        <HStack mb={3} spacing={2}>
                          <Text fontSize="xs" color="gray.500" fontWeight="medium">
                            {resource.duration}
                          </Text>
                          <Text fontSize="xs" color="gray.400">•</Text>
                          <Text fontSize="xs" color="gray.500" fontWeight="medium">
                            {resource.level}
                          </Text>
                          {resource.premium && (
                            <>
                              <Text fontSize="xs" color="gray.400">•</Text>
                              <Text fontSize="xs" color="purple.500" fontWeight="medium">
                                Premium
                              </Text>
                            </>
                          )}
                        </HStack>

                        {/* Clean title */}
                        <Heading
                          size="md"
                          mb={3}
                          lineHeight="1.4"
                          fontWeight="600"
                          color="gray.800"
                          _dark={{ color: "white" }}
                        >
                          {resource.title}
                        </Heading>

                        {/* Clean description */}
                        <Text
                          fontSize="sm"
                          mb={4}
                          lineHeight="1.5"
                          color="gray.600"
                          _dark={{ color: "gray.300" }}
                        >
                          {resource.description}
                        </Text>

                        {/* Minimalistic progress tracker */}
                        {resource.progress > 0 && (
                          <Box mb={4}>
                            <Flex align="center" justify="space-between" mb={1}>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Progress
                              </Text>
                              <Text fontSize="xs" color="gray.600" fontWeight="medium">
                                {resource.progress}%
                              </Text>
                            </Flex>
                            <Progress
                              value={resource.progress}
                              size="xs"
                              colorScheme="purple"
                              borderRadius="full"
                              bg="gray.100"
                              _dark={{ bg: "gray.700" }}
                            />
                          </Box>
                        )}

                        {/* Modern minimalistic buttons */}
                        <HStack spacing={3} mt={4}>
                          <Button
                            size="sm"
                            bg={resource.completed ? "transparent" : "gray.900"}
                            color={resource.completed ? "gray.600" : "white"}
                            border={resource.completed ? "1px solid" : "none"}
                            borderColor={resource.completed ? "gray.300" : "transparent"}
                            leftIcon={resource.completed ? <FaCheck /> : <FaPlay />}
                            isDisabled={resource.premium}
                            onClick={() => resource.type === 'video' ? handleVideoPlay(resource) : undefined}
                            borderRadius="lg"
                            fontWeight="500"
                            fontSize="sm"
                            px={4}
                            py={2}
                            h="auto"
                            _hover={{
                              transform: 'translateY(-1px)',
                              bg: resource.completed ? "gray.50" : "gray.800",
                              _dark: {
                                bg: resource.completed ? "gray.700" : "gray.600"
                              }
                            }}
                            _dark={{
                              bg: resource.completed ? "transparent" : "white",
                              color: resource.completed ? "gray.400" : "gray.900",
                              borderColor: resource.completed ? "gray.600" : "transparent"
                            }}
                            transition="all 0.2s ease"
                          >
                            {resource.completed ? "Completed" : "Watch"}
                          </Button>

                          <Button
                            size="sm"
                            variant="ghost"
                            color="gray.500"
                            fontWeight="400"
                            fontSize="sm"
                            px={3}
                            py={2}
                            h="auto"
                            borderRadius="lg"
                            _hover={{
                              color: 'gray.700',
                              bg: 'gray.50',
                              _dark: {
                                color: 'gray.300',
                                bg: 'gray.700'
                              }
                            }}
                            transition="all 0.2s ease"
                          >
                            Details
                          </Button>
                        </HStack>
                      </Box>
                    </Box>
                  ))}
                </SimpleGrid>

                {(category === 'Saved' && savedResources.length === 0) && (
                  <Text textAlign="center" py={10} color="gray.500">
                    You haven't saved any resources yet. Browse the learning center and bookmark resources to save them.
                  </Text>
                )}

                {(category !== 'Saved' && filteredResources.length === 0) && (
                  <Text textAlign="center" py={10} color="gray.500">
                    No resources match your search criteria.
                  </Text>
                )}
              </TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </ContentContainer>

      {/* Video Modal */}
      <Modal isOpen={isOpen} onClose={handleModalClose} size="4xl" isCentered>
        <ModalOverlay backdropFilter="auto" backdropInvert="15%" backdropBlur="2px" />
        <ModalContent maxW="80vw" maxH="80vh">
          <ModalHeader>
            {selectedVideo?.title}
            <Badge ml={2} colorScheme="purple">
              {selectedVideo?.subcategory}
            </Badge>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {selectedVideo && (
              <VStack spacing={4}>
                <Box width="100%" maxW="800px">
                  <VideoEmbed
                    videoUrl={selectedVideo.videoUrl}
                    videoType={selectedVideo.videoType}
                    title={selectedVideo.title}
                    showPreview={false}
                  />
                </Box>
                <Box width="100%" textAlign="left">
                  <Text fontSize="md" mb={2}>
                    {selectedVideo.description}
                  </Text>
                  <HStack spacing={2} mb={3}>
                    <Badge colorScheme="blue">{selectedVideo.level}</Badge>
                    <Badge colorScheme="green">{selectedVideo.duration}</Badge>
                    <Badge>{selectedVideo.category}</Badge>
                  </HStack>
                  <HStack spacing={4} mt={6}>
                    <Button
                      size="md"
                      bg={selectedVideo.completed ? "transparent" : "green.500"}
                      color={selectedVideo.completed ? "green.600" : "white"}
                      border={selectedVideo.completed ? "1px solid" : "none"}
                      borderColor={selectedVideo.completed ? "green.300" : "transparent"}
                      leftIcon={<FaCheck />}
                      onClick={() => handleVideoComplete(selectedVideo)}
                      isDisabled={selectedVideo.completed}
                      borderRadius="lg"
                      fontWeight="500"
                      px={6}
                      py={3}
                      h="auto"
                      _hover={{
                        transform: 'translateY(-1px)',
                        bg: selectedVideo.completed ? "green.50" : "green.600",
                        _dark: {
                          bg: selectedVideo.completed ? "green.900" : "green.400"
                        }
                      }}
                      _dark={{
                        bg: selectedVideo.completed ? "transparent" : "green.400",
                        borderColor: selectedVideo.completed ? "green.600" : "transparent"
                      }}
                      transition="all 0.2s ease"
                    >
                      {selectedVideo.completed ? "Completed" : "Mark Complete"}
                    </Button>
                    <VStack align="start" spacing={0}>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Progress
                      </Text>
                      <Text fontSize="sm" color="gray.700" fontWeight="600" _dark={{ color: "gray.300" }}>
                        {selectedVideo.progress}%
                      </Text>
                    </VStack>
                  </HStack>
                </Box>
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
      </VStack>
    </ContentPageBox>
  );
}
