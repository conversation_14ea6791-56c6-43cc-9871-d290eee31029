import { type User } from 'wasp/entities';
import { logout } from 'wasp/client/auth';

import { stripePayment, stripeGpt4Payment, useQuery, getUserInfo, getUserBadges, getLearningProgress, getUserPreferences } from 'wasp/client/operations';

import ContentPageBox from './components/ContentPageBox';
import ContentContainer from './components/ContentContainer';
import ActionButton from './components/ActionButton';
import BadgeDisplay from './components/BadgeDisplay';
import AccountInfoCard from './components/ProfileSettings/AccountInfoCard';
import PreferencesCard from './components/ProfileSettings/PreferencesCard';
import NotificationsCard from './components/ProfileSettings/NotificationsCard';
import DangerZoneCard from './components/ProfileSettings/DangerZoneCard';
import {
  Box,
  Heading,
  Text,
  Code,
  Spinner,
  VStack,
  HStack,
  Link,
  Grid,
  Button,
  Divider,
  Avatar,
  Flex,
  Badge,
  Icon,
  useColorModeValue
} from '@chakra-ui/react';
import { useState } from 'react';
import { IoWarningOutline } from 'react-icons/io5';
import {
  FaSignOutAlt,
  FaShoppingCart,
  FaTrophy,
} from 'react-icons/fa';

export default function ProfilePage({ user }: { user: User }) {
  const [isLoading, setIsLoading] = useState(false);
  const [isGpt4loading, setIsGpt4Loading] = useState(false);

  const { data: userInfo, refetch: refetchUserInfo } = useQuery(getUserInfo, { id: user.id });
  const { data: userBadges, refetch: refetchUserBadges } = useQuery(getUserBadges);
  const { data: learningProgress, refetch: refetchLearningProgress } = useQuery(getLearningProgress);

  const handleDataRefresh = () => {
    refetchUserInfo();
    refetchUserBadges();
    refetchLearningProgress();
  };

  const userPaidOnDay = new Date(String(user.datePaid));
  const oneMonthFromDatePaid = new Date(userPaidOnDay.setMonth(userPaidOnDay.getMonth() + 1));



  async function handleBuy4oMini() {
    setIsLoading(true);
    try {
      const response = await stripePayment();
      const url = response.sessionUrl;
      if (url) window.open(url, '_self');
    } catch (error) {
      alert('Something went wrong. Please try again');
    }
    setIsLoading(false);
  }

  async function handleBuy4o() {
    setIsGpt4Loading(true);
    try {
      const response = await stripeGpt4Payment();
      const url = response.sessionUrl;
      if (url) window.open(url, '_self');
    } catch (error) {
      alert('Something went wrong. Please try again');
    }
    setIsGpt4Loading(false);
  }

  return (
    <ContentPageBox>
      {!!userInfo ? (
        <>
          {/* Modern Profile Header */}
          <Box
            bg="gradient-to-r"
            bgGradient="linear(to-r, blue.500, purple.600)"
            borderRadius="2xl"
            p={8}
            color="white"
            position="relative"
            overflow="hidden"
            mb={8}
          >
            {/* Background Pattern */}
            <Box
              position="absolute"
              top="-50%"
              right="-20%"
              width="300px"
              height="300px"
              borderRadius="full"
              bg="whiteAlpha.100"
              opacity={0.3}
            />
            <Box
              position="absolute"
              bottom="-30%"
              left="-10%"
              width="200px"
              height="200px"
              borderRadius="full"
              bg="whiteAlpha.100"
              opacity={0.2}
            />

            <Flex justify="space-between" align="center" position="relative" zIndex={1}>
              <HStack spacing={6}>
                <Avatar
                  size="xl"
                  name={user?.username || userInfo?.email || 'User'}
                  src={undefined}
                  bg="whiteAlpha.200"
                  color="white"
                  border="4px solid"
                  borderColor="whiteAlpha.300"
                />
                <VStack align="start" spacing={1}>
                  <Heading size="lg" fontWeight="bold">
                    {user?.username || 'User'}
                  </Heading>
                  <Text fontSize="md" opacity={0.9}>
                    {userInfo?.email}
                  </Text>
                  <HStack spacing={2} mt={2}>
                    <Badge colorScheme="green" variant="solid" borderRadius="full" px={3}>
                      {userInfo?.hasPaid ? 'Premium' : 'Free Plan'}
                    </Badge>
                    {userInfo?.gptModel && (
                      <Badge colorScheme="purple" variant="solid" borderRadius="full" px={3}>
                        {userInfo.gptModel.toUpperCase()}
                      </Badge>
                    )}
                  </HStack>
                </VStack>
              </HStack>

              <ActionButton
                icon={FaSignOutAlt}
                label="Logout"
                variant="outline"
                onClick={() => logout()}
                colorScheme="whiteAlpha"
                size="lg"
              />
            </Flex>
          </Box>

          <ContentContainer delay={0.3}>
            {userInfo.subscriptionStatus === 'past_due' ? (
              <VStack gap={3} py={5} alignItems='center'>
                <Box color='blue.400'>
                  <IoWarningOutline size={30} color='inherit' />
                </Box>
                <Text textAlign='center' fontSize='sm' textColor='text-contrast-lg'>
                  Your subscription is past due. <br /> Please update your payment method{' '}
                  <Link textColor='blue.400' href='https://billing.stripe.com/p/login/5kA7sS0Wc3gD2QM6oo'>
                    by clicking here
                  </Link>
                </Text>
              </VStack>
            ) : userInfo.hasPaid && !userInfo.isUsingLn ? (
              <VStack gap={3} pt={2} alignItems='flex-start'>
                <Text textAlign='initial'>Thanks so much for your support!</Text>

                <Text textAlign='initial'>You have unlimited access to CareerDart using {user?.gptModel === 'gpt-4' || user?.gptModel === 'gpt-4o' ? 'GPT-4o.' : 'GPT-4o-mini.'}</Text>

                {userInfo.subscriptionStatus === 'canceled' && (
                  <Code alignSelf='center' fontSize='lg'>
                    {oneMonthFromDatePaid.toUTCString().slice(0, -13)}
                  </Code>
                )}
                <Text alignSelf='initial' fontSize='sm' fontStyle='italic' textColor='text-contrast-sm'>
                  To manage your subscription, please{' '}
                  <Link textColor='blue.600' href='https://billing.stripe.com/p/login/5kA7sS0Wc3gD2QM6oo'>
                    click here.
                  </Link>
                </Text>
              </VStack>
            ) : (
              !userInfo.isUsingLn && (
                <HStack pt={2} textAlign='center'>
                  <Heading size='sm'>You have </Heading>
                  <Code>{userInfo?.credits ? userInfo.credits : '0'}</Code>
                  <Heading size='sm'>cover letter{userInfo?.credits === 1 ? '' : 's'} left</Heading>
                </HStack>
              )
            )}
          </ContentContainer>

          {!userInfo.hasPaid && !userInfo.isUsingLn && (
            <ContentContainer delay={0.4}>
              {/* Compact Subscription Row */}
              <Box
                bg={useColorModeValue('white', 'gray.800')}
                borderRadius="2xl"
                p={6}
                boxShadow="xl"
                borderWidth="1px"
                borderColor={useColorModeValue('gray.100', 'gray.700')}
                mb={6}
              >
                <HStack spacing={4} mb={4}>
                  <Box
                    p={3}
                    borderRadius="xl"
                    bg={useColorModeValue('green.50', 'green.900')}
                    color={useColorModeValue('green.600', 'green.300')}
                  >
                    <Icon as={FaShoppingCart} boxSize={5} />
                  </Box>
                  <VStack align="start" spacing={0}>
                    <Heading size="md" color={useColorModeValue('gray.800', 'white')}>
                      Upgrade to Premium
                    </Heading>
                    <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                      Choose your plan and unlock unlimited access
                    </Text>
                  </VStack>
                </HStack>

                <HStack spacing={4} align="stretch">
                  {/* Basic Plan */}
                  <Box
                    flex={1}
                    p={5}
                    borderRadius="xl"
                    bg={useColorModeValue('gray.50', 'gray.700')}
                    borderWidth="2px"
                    borderColor="transparent"
                    transition="all 0.3s ease"
                    _hover={{
                      borderColor: useColorModeValue('blue.300', 'blue.500'),
                      transform: 'translateY(-2px)'
                    }}
                  >
                    <VStack spacing={3} align="stretch">
                      <HStack justify="space-between" align="center">
                        <VStack align="start" spacing={1}>
                          <HStack>
                            <Text fontSize="2xl" fontWeight="bold" color={useColorModeValue('gray.800', 'white')}>
                              $2.95
                            </Text>
                            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                              /month
                            </Text>
                          </HStack>
                          <Text fontSize="sm" fontWeight="medium" color={useColorModeValue('gray.700', 'gray.300')}>
                            GPT-4o-mini • Unlimited
                          </Text>
                        </VStack>
                        <Text fontSize="lg">🚀</Text>
                      </HStack>
                      <Button
                        colorScheme="blue"
                        variant="outline"
                        size="sm"
                        borderRadius="lg"
                        isLoading={isLoading}
                        onClick={handleBuy4oMini}
                        _hover={{ bg: 'blue.50' }}
                      >
                        Get Started
                      </Button>
                    </VStack>
                  </Box>

                  {/* Premium Plan */}
                  <Box
                    flex={1}
                    p={5}
                    borderRadius="xl"
                    bg={useColorModeValue('blue.50', 'blue.900')}
                    borderWidth="2px"
                    borderColor={useColorModeValue('blue.300', 'blue.500')}
                    position="relative"
                    transition="all 0.3s ease"
                    _hover={{
                      borderColor: useColorModeValue('blue.400', 'blue.400'),
                      transform: 'translateY(-2px)'
                    }}
                  >
                    <Badge
                      position="absolute"
                      top="-8px"
                      left="50%"
                      transform="translateX(-50%)"
                      colorScheme="blue"
                      variant="solid"
                      borderRadius="full"
                      px={3}
                      py={1}
                      fontSize="xs"
                    >
                      POPULAR
                    </Badge>
                    <VStack spacing={3} align="stretch">
                      <HStack justify="space-between" align="center">
                        <VStack align="start" spacing={1}>
                          <HStack>
                            <Text fontSize="2xl" fontWeight="bold" color={useColorModeValue('blue.800', 'blue.200')}>
                              $5.95
                            </Text>
                            <Text fontSize="sm" color={useColorModeValue('blue.600', 'blue.400')}>
                              /month
                            </Text>
                          </HStack>
                          <Text fontSize="sm" fontWeight="medium" color={useColorModeValue('blue.700', 'blue.300')}>
                            GPT-4o • Unlimited
                          </Text>
                        </VStack>
                        <Text fontSize="lg">🤖</Text>
                      </HStack>
                      <Button
                        colorScheme="blue"
                        size="sm"
                        borderRadius="lg"
                        isLoading={isGpt4loading}
                        onClick={handleBuy4o}
                        _hover={{ bg: 'blue.600' }}
                      >
                        Upgrade Now
                      </Button>
                    </VStack>
                  </Box>
                </HStack>
              </Box>
            </ContentContainer>
          )}

          {userInfo.isUsingLn && (
            <ContentContainer delay={0.4}>
              {/* Lightning Network Section */}
              <Box
                bg={useColorModeValue('yellow.50', 'yellow.900')}
                borderRadius="2xl"
                p={6}
                borderWidth="2px"
                borderColor={useColorModeValue('yellow.200', 'yellow.600')}
                mb={6}
              >
                <HStack spacing={4} align="center">
                  <Box
                    p={3}
                    borderRadius="xl"
                    bg={useColorModeValue('yellow.100', 'yellow.800')}
                    color={useColorModeValue('yellow.600', 'yellow.300')}
                  >
                    <Text fontSize="2xl">⚡️</Text>
                  </Box>
                  <VStack align="start" spacing={1} flex={1}>
                    <Heading size="md" color={useColorModeValue('yellow.800', 'yellow.200')}>
                      Lightning Network Access
                    </Heading>
                    <Text fontSize="sm" color={useColorModeValue('yellow.700', 'yellow.300')}>
                      Pay-per-use access with GPT-4o via Lightning Network
                    </Text>
                    <Text fontSize="xs" color={useColorModeValue('yellow.600', 'yellow.400')}>
                      Want monthly subscription? Logout and sign in with Google
                    </Text>
                  </VStack>
                  <Badge
                    colorScheme="yellow"
                    variant="solid"
                    borderRadius="full"
                    px={3}
                    py={1}
                  >
                    ACTIVE
                  </Badge>
                </HStack>
              </Box>
            </ContentContainer>
          )}

          {/* Modern Settings Grid */}
          <ContentContainer delay={0.5}>
            <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={4}>

              {/* Account Information Card */}
              <AccountInfoCard userInfo={userInfo} onUpdate={handleDataRefresh} />

              {/* Preferences Card */}
              <PreferencesCard userInfo={userInfo} onUpdate={handleDataRefresh} />

              {/* Notifications Card */}
              <NotificationsCard userInfo={userInfo} onUpdate={handleDataRefresh} />

            </Grid>

            {/* Achievements Section - Minimalistic */}
            <Box
              bg={useColorModeValue('white', 'gray.800')}
              borderRadius="xl"
              p={5}
              borderWidth="1px"
              borderColor={useColorModeValue('gray.200', 'gray.700')}
              mt={4}
            >
              <HStack spacing={3} mb={4} align="center">
                <Icon as={FaTrophy} boxSize={4} color={useColorModeValue('gray.600', 'gray.400')} />
                <Heading size="md" color={useColorModeValue('gray.800', 'white')} fontWeight="medium">
                  Progress
                </Heading>
              </HStack>

              <HStack spacing={6} justify="space-around">
                {/* Videos */}
                <VStack spacing={1} align="center">
                  <Text fontSize="2xl" fontWeight="bold" color={useColorModeValue('gray.800', 'white')}>
                    {learningProgress?.filter((p: any) => p.completed).length || 0}
                  </Text>
                  <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                    Videos
                  </Text>
                </VStack>

                <Divider orientation="vertical" height="40px" />

                {/* Minutes */}
                <VStack spacing={1} align="center">
                  <Text fontSize="2xl" fontWeight="bold" color={useColorModeValue('gray.800', 'white')}>
                    {Math.floor((learningProgress?.reduce((sum: number, p: any) => sum + p.timeSpent, 0) || 0) / 60)}
                  </Text>
                  <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                    Minutes
                  </Text>
                </VStack>

                <Divider orientation="vertical" height="40px" />

                {/* Badges */}
                <VStack spacing={1} align="center">
                  <Text fontSize="2xl" fontWeight="bold" color={useColorModeValue('gray.800', 'white')}>
                    {userBadges?.length || 0}
                  </Text>
                  <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                    Badges
                  </Text>
                </VStack>
              </HStack>

              {/* Simplified Badges - Only show if there are badges */}
              {userBadges && userBadges.length > 0 && (
                <>
                  <Divider my={4} />
                  <BadgeDisplay
                    badges={userBadges?.map((ub: any) => ({
                      id: ub.badge.id,
                      name: ub.badge.name,
                      description: ub.badge.description,
                      icon: ub.badge.icon,
                      category: ub.badge.category,
                      color: ub.badge.color,
                      earnedAt: ub.earnedAt
                    })) || []}
                    title=""
                  />
                </>
              )}
            </Box>

            {/* Danger Zone - Separate Section */}
            <DangerZoneCard userInfo={userInfo} />
          </ContentContainer>
        </>
      ) : (
        <VStack py={10}>
          <Spinner size="xl" />
          <Text mt={4}>Loading profile...</Text>
        </VStack>
      )}
    </ContentPageBox>
  );
}
