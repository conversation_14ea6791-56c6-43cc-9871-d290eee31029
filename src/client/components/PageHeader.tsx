import React from 'react';
import {
  Box,
  Flex,
  Heading,
  HStack,
  Text,
  useColorModeValue,
  FlexProps,
  IconButton,
  Tooltip,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { useSidebar } from './NavBar';

// Create motion components
const MotionBox = motion(Box);
const MotionHeading = motion(Heading);
const MotionText = motion(Text);

interface PageHeaderProps extends FlexProps {
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
}

/**
 * Modern page header component with title, optional subtitle, and action buttons
 * Designed with Apple-inspired aesthetics
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  children,
  ...rest
}) => {
  // Get sidebar context
  const { isCollapsed, toggleCollapse } = useSidebar();

  // Modern color scheme
  const bgGradient = useColorModeValue(
    'linear(to-r, blue.50, cyan.100, blue.50)',
    'linear(to-r, blue.900, cyan.800, blue.900)'
  );

  const titleGradient = useColorModeValue(
    'linear(to-r, blue.600, cyan.700)',
    'linear(to-r, blue.400, cyan.600)'
  );

  const subtitleColor = useColorModeValue('gray.600', 'gray.300');
  const buttonBg = useColorModeValue('white', 'gray.700');
  const buttonHoverBg = useColorModeValue('gray.100', 'gray.600');

  return (
    <MotionBox
      position="relative"
      mb={{ base: 3, md: 6 }}
      pb={{ base: 1, md: 2 }}
      initial={{ opacity: 0, y: -5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Subtle left accent instead of full background */}
      <Box
        position="absolute"
        top={0}
        left={{ base: 1, md: 0 }} // Slight offset on mobile to ensure visibility
        width={{ base: "3px", md: "4px" }}
        height="100%"
        bgGradient={bgGradient}
        opacity={{ base: 0.8, md: 0.7 }} // Slightly more visible on mobile
        borderRadius="sm"
        zIndex={0}
      />

      {/* Sidebar toggle button */}
      <Tooltip
        label={isCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
        placement="right"
        hasArrow
      >
        <IconButton
          aria-label={isCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
          icon={isCollapsed ? <FaChevronRight size="12px" /> : <FaChevronLeft size="12px" />}
          onClick={toggleCollapse}
          position="absolute"
          left={{ base: 1, md: 2 }}
          top={{ base: 2, md: 3 }}
          size="sm"
          variant="ghost"
          colorScheme="purple"
          bg={buttonBg}
          _hover={{ bg: buttonHoverBg }}
          display={{ base: 'none', md: 'flex' }}
          zIndex={2}
          borderRadius="full"
          boxShadow="sm"
        />
      </Tooltip>

      {/* Content container */}
      <Flex
        direction={{ base: 'column', md: 'row' }}
        justify="space-between"
        align={{ base: 'flex-start', md: 'center' }}
        wrap="nowrap"
        gap={{ base: 1, md: 4 }}
        py={{ base: 2, md: 3 }}
        pl={{ base: 4, sm: 5, md: 10 }} // Increased left padding to make room for the toggle button
        pr={{ base: 4, sm: 5, md: 6 }} // Consistent right padding
        position="relative"
        zIndex={1}
        minH={{ base: "auto", md: "40px" }}
        width="100%"
        {...rest}
      >
        {/* Title and subtitle */}
        <MotionBox
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          flexShrink={1}
          maxW={{ base: "100%", md: "60%" }}
        >
          <MotionHeading
            as="h1"
            size={{ base: "sm", md: "md" }}
            fontWeight="semibold"
            letterSpacing="tight"
            color="gray.800"
            _dark={{ color: "gray.100" }}
            mb={subtitle ? { base: 0.5, md: 1 } : 0}
            lineHeight={{ base: "1.3", md: "1.4" }}
          >
            {title}
          </MotionHeading>

          {subtitle && (
            <MotionText
              fontSize={{ base: "xs", md: "sm" }}
              fontWeight="normal"
              color={subtitleColor}
              maxW="container.md"
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.9 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              lineHeight={{ base: "1.3", md: "1.5" }}
            >
              {subtitle}
            </MotionText>
          )}
        </MotionBox>

        {/* Action buttons */}
        {children && (
          <MotionBox
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.2 }}
            ml={{ base: 0, md: "auto" }}
            display="flex"
            alignItems="center"
            height={{ base: "auto", md: "28px" }}
            minW="auto"
            flexShrink={0}
            width={{ base: "100%", md: "auto" }}
          >
            <HStack
              spacing={{ base: 2, md: 3 }}
              mt={{ base: 2, md: 0 }}
              alignItems="center"
              justifyContent={{ base: "flex-start", md: "flex-end" }}
              minW="auto"
              flexShrink={0}
              width={{ base: "100%", md: "auto" }}
              flexWrap={{ base: "wrap", md: "nowrap" }}
            >
              {children}
            </HStack>
          </MotionBox>
        )}
      </Flex>

    </MotionBox>
  );
};

export default PageHeader;
