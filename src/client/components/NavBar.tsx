import { useAuth } from "wasp/client/auth";
import {
  HStack,
  Button,
  Link,
  MenuList,
  MenuItem,
  Menu,
  MenuButton,
  StackProps,
  useColorModeValue,
  Box,
  Flex,
  Container,
  useDisclosure,
  Drawer,
  DrawerBody,
  DrawerHeader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
  IconButton,
  VStack,
  Icon,
  Text,
  useBreakpointValue
} from '@chakra-ui/react';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import { CgProfile } from 'react-icons/cg';
import { AiOutlineMenu } from 'react-icons/ai';
import { FaHome, FaFileAlt, FaBriefcase, FaChartLine, FaComments, FaGraduationCap } from 'react-icons/fa';
import ThemeSwitch from './ThemeSwitch';
import Logo from './Logo';
import SkipNavigation from './SkipNavigation';
import { createContext, useContext } from 'react';

// Create a context to manage sidebar visibility across components
export interface SidebarContextType {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  isCollapsed: boolean;
  toggleCollapse: () => void;
}

export const SidebarContext = createContext<SidebarContextType>({
  isSidebarOpen: false,
  toggleSidebar: () => {},
  closeSidebar: () => {},
  isCollapsed: false,
  toggleCollapse: () => {}
});

export const useSidebar = () => useContext(SidebarContext);

interface NavLinkProps extends StackProps {
  children: React.ReactNode;
  path: string;
}

const Links = [
  { name: 'Cover Letters', path: '/cover-letters' },
  { name: 'Jobs', path: '/jobs' },
  { name: 'Resume', path: '/resume' },
];

const NavLink = ({ children, path, ...props }: NavLinkProps) => {
  const location = useLocation();
  const isActive = location.pathname === path;
  const activeBg = useColorModeValue('blue.50', 'blue.900');
  const activeColor = useColorModeValue('blue.700', 'blue.300');
  const hoverBg = useColorModeValue('gray.100', 'gray.700');

  return (
    <Box
      as={RouterLink}
      px={3}
      py={2}
      rounded={'md'}
      fontWeight={isActive ? 'semibold' : 'medium'}
      color={isActive ? activeColor : 'inherit'}
      bg={isActive ? activeBg : 'transparent'}
      transition="all 0.2s"
      _hover={{
        textDecoration: 'none',
        bg: isActive ? activeBg : hoverBg,
        transform: 'translateY(-1px)',
      }}
      to={path}
      {...props}
    >
      {children}
    </Box>
  );
};

export default function NavBar() {
  const { data: user } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { toggleSidebar } = useSidebar();

  // Responsive design
  const showMobileMenu = useBreakpointValue({ base: true, md: false });
  const showDesktopMenu = useBreakpointValue({ base: false, md: true });

  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const bgColor = useColorModeValue('rgba(255, 255, 255, 0.95)', 'rgba(26, 32, 44, 0.95)');
  const mobileMenuBg = useColorModeValue('white', 'gray.800');

  return (
    <>
      <SkipNavigation />
      <Box
        as='nav'
        id="navigation"
        role="navigation"
        aria-label="Main navigation"
        py={4}
        top={0}
        width='full'
        position='sticky'
        backdropFilter='blur(10px)'
        borderBottom='md'
        borderColor={borderColor}
        boxShadow='sm'
        color='text-contrast-lg'
        zIndex={10}
        bg={bgColor}
      >
      <Container maxW='container.xl'>
        <Flex align='center' justify='space-between'>
          {/* Logo - Far Left */}
          <Box flex="0 0 auto">
            <Link as={RouterLink} to='/' _hover={{ textDecoration: 'none' }}>
              <Logo />
            </Link>
          </Box>

          {/* Desktop Nav Links - Centered */}
          <Flex
            justify='center'
            flex={1}
            display={{ base: 'none', md: 'flex' }}
            mx={8}
          >
            <HStack spacing={4}>
              {Links.map((link) => (
                <NavLink key={link.name} path={link.path}>
                  {link.name}
                </NavLink>
              ))}
            </HStack>
          </Flex>

          {/* Right Section: Theme Switch and Auth Buttons */}
          <HStack spacing={4} alignItems='center' flex="0 0 auto">
            <ThemeSwitch />
            {user ? (
              <HStack spacing={3} display={{ base: 'none', md: 'flex' }}>
                <NavButton icon={<CgProfile />} to='/profile'>
                  Profile
                </NavButton>
              </HStack>
            ) : (
              <Button
                as={RouterLink}
                to='/login'
                colorScheme='blue'
                size='sm'
                leftIcon={<CgProfile />}
                display={{ base: 'none', md: 'flex' }}
              >
                Login
              </Button>
            )}

            {/* Mobile Menu Button */}
            <IconButton
              display={{ base: 'flex', md: 'none' }}
              onClick={() => {
                toggleSidebar(); // Toggle sidebar drawer
                onOpen(); // Also open the dropdown menu
              }}
              variant='ghost'
              aria-label='Open menu'
              size='sm'
              icon={<AiOutlineMenu />}
            />
          </HStack>
        </Flex>
      </Container>

      {/* Mobile Menu */}
      <Menu isOpen={isOpen} onClose={onClose}>
        <MenuList
          bg={mobileMenuBg}
          p={4}
          boxShadow='lg'
          borderRadius='md'
          border='1px solid'
          borderColor={borderColor}
          display={{ base: isOpen ? 'block' : 'none', md: 'none' }}
          position='absolute'
          top='60px'
          right='10px'
          width='200px'
          zIndex={100}
        >
          {Links.map((link) => (
            <Link
              as={RouterLink}
              to={link.path}
              key={link.name}
              display='block'
              onClick={onClose}
            >
              <MenuItem
                py={2}
                borderRadius='md'
                _hover={{ bg: 'blue.50', color: 'blue.600' }}
              >
                {link.name}
              </MenuItem>
            </Link>
          ))}
          <Box pt={2} mt={2} borderTop='1px solid' borderColor={borderColor}>
            {user ? (
              <Link as={RouterLink} to='/profile' onClick={onClose}>
                <MenuItem py={2}>Profile</MenuItem>
              </Link>
            ) : (
              <Link as={RouterLink} to='/login' onClick={onClose}>
                <MenuItem py={2} fontWeight='medium' color='blue.500'>Login</MenuItem>
              </Link>
            )}
          </Box>
        </MenuList>
      </Menu>
      </Box>
    </>
  );
}

interface NavButtonProps {
  children: React.ReactNode;
  icon: React.ReactElement;
  to: string;
}

function NavButton({ children, icon, to }: NavButtonProps) {
  const hoverBg = useColorModeValue('blue.50', 'blue.900');
  const hoverColor = useColorModeValue('blue.700', 'blue.300');

  return (
    <Link
      as={RouterLink}
      to={to}
      display="flex"
      alignItems="center"
      px={3}
      py={2}
      borderRadius="full"
      fontWeight="medium"
      _hover={{
        textDecoration: 'none',
        bg: hoverBg,
        color: hoverColor,
        transform: 'translateY(-1px)'
      }}
    >
      <HStack spacing={2}>
        {icon}
        <Box>{children}</Box>
      </HStack>
    </Link>
  );
}
