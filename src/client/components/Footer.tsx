import React from 'react';
import {
  Box,
  Container,
  Flex,
  HStack,
  Link,
  SimpleGrid,
  Stack,
  Text,
  VStack,
  Icon,
  Divider,
  useColorModeValue,
  Button,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import { FaGithub, FaTwitter, FaLinkedin, FaEnvelope, FaHeart } from 'react-icons/fa';
import Logo from './Logo';
import ThemeSwitch from './ThemeSwitch';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const linkColor = useColorModeValue('teal.600', 'teal.300');
  const hoverColor = useColorModeValue('teal.700', 'teal.200');

  return (
    <Box
      as="footer"
      id="footer"
      role="contentinfo"
      aria-label="Site footer"
      width="100%"
      bg={bgColor}
      borderTop="1px"
      borderColor={borderColor}
      py={6}
      mt={8}
    >
      <Container maxW="container.xl">
        <SimpleGrid
          columns={{ base: 1, md: 4 }}
          spacing={{ base: 8, md: 4 }}
          py={4}
        >
          {/* Logo and About */}
          <VStack align="flex-start" spacing={4}>
            <Logo />
            <Text fontSize="sm" color={textColor}>
              Your AI-powered career companion. Build resumes, craft cover letters, and prepare for interviews.
            </Text>
            <HStack spacing={4}>
              <Link
                href="https://github.com"
                isExternal
                aria-label="Follow CareerDart on GitHub"
              >
                <Icon as={FaGithub} boxSize={5} color={textColor} _hover={{ color: hoverColor }} />
              </Link>
              <Link
                href="https://twitter.com"
                isExternal
                aria-label="Follow CareerDart on Twitter"
              >
                <Icon as={FaTwitter} boxSize={5} color={textColor} _hover={{ color: hoverColor }} />
              </Link>
              <Link
                href="https://linkedin.com"
                isExternal
                aria-label="Follow CareerDart on LinkedIn"
              >
                <Icon as={FaLinkedin} boxSize={5} color={textColor} _hover={{ color: hoverColor }} />
              </Link>
              <Link
                href="mailto:<EMAIL>"
                aria-label="Send email to CareerDart support"
              >
                <Icon as={FaEnvelope} boxSize={5} color={textColor} _hover={{ color: hoverColor }} />
              </Link>
            </HStack>
          </VStack>

          {/* Quick Links */}
          <VStack align="flex-start" spacing={2}>
            <Text fontWeight="bold" fontSize="md" mb={2}>
              Quick Links
            </Text>
            <Link as={RouterLink} to="/dashboard" color={textColor} _hover={{ color: linkColor }}>
              Dashboard
            </Link>
            <Link as={RouterLink} to="/resume" color={textColor} _hover={{ color: linkColor }}>
              Resume Builder
            </Link>
            <Link as={RouterLink} to="/cover-letter" color={textColor} _hover={{ color: linkColor }}>
              Cover Letter Generator
            </Link>
            <Link as={RouterLink} to="/jobs" color={textColor} _hover={{ color: linkColor }}>
              Job Tracker
            </Link>
            <Link as={RouterLink} to="/interview-prep" color={textColor} _hover={{ color: linkColor }}>
              Interview Preparation
            </Link>
          </VStack>

          {/* Resources */}
          <VStack align="flex-start" spacing={2}>
            <Text fontWeight="bold" fontSize="md" mb={2}>
              Resources
            </Text>
            <Link href="#" color={textColor} _hover={{ color: linkColor }}>
              Career Blog
            </Link>
            <Link href="#" color={textColor} _hover={{ color: linkColor }}>
              Resume Templates
            </Link>
            <Link href="#" color={textColor} _hover={{ color: linkColor }}>
              Interview Tips
            </Link>
            <Link href="#" color={textColor} _hover={{ color: linkColor }}>
              Salary Guides
            </Link>
            <Link href="#" color={textColor} _hover={{ color: linkColor }}>
              Career Development
            </Link>
          </VStack>

          {/* Company */}
          <VStack align="flex-start" spacing={2}>
            <Text fontWeight="bold" fontSize="md" mb={2}>
              Company
            </Text>
            <Link href="#" color={textColor} _hover={{ color: linkColor }}>
              About Us
            </Link>
            <Link as={RouterLink} to="/help" color={textColor} _hover={{ color: linkColor }}>
              Help Center
            </Link>
            <Link as={RouterLink} to="/privacy" color={textColor} _hover={{ color: linkColor }}>
              Privacy Policy
            </Link>
            <Link as={RouterLink} to="/tos" color={textColor} _hover={{ color: linkColor }}>
              Terms of Service
            </Link>
            <Link href="mailto:<EMAIL>" color={textColor} _hover={{ color: linkColor }}>
              Contact Us
            </Link>
            <HStack mt={2}>
              <ThemeSwitch />
              <Text fontSize="sm" color={textColor}>
                Toggle Theme
              </Text>
            </HStack>
          </VStack>
        </SimpleGrid>

        <Divider my={4} borderColor={borderColor} />

        <Flex
          direction={{ base: 'column', md: 'row' }}
          justify="space-between"
          align="center"
          pt={2}
        >
          <Text fontSize="sm" color={textColor}>
            © {currentYear} CareerDart. All rights reserved.
          </Text>
          <Text fontSize="sm" color={textColor} mt={{ base: 2, md: 0 }}>
            Made with <Icon as={FaHeart} color="red.500" mx={1} /> by CareerDart Team
          </Text>
        </Flex>
      </Container>
    </Box>
  );
};

export default Footer;
