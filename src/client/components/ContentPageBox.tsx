import { Box, VStack, BoxProps, useBreakpointValue, useColorModeValue } from '@chakra-ui/react';
import { useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

// Create motion components
const MotionBox = motion(Box);

interface ContentPageBoxProps extends BoxProps {
  children: React.ReactNode;
}

/**
 * ContentPageBox is a specialized container for content pages (Resume, Jobs, Cover Letter, Profile)
 * that extends the width to align with the logo and removes borders
 * Enhanced with responsive design and subtle animations
 */
export default function ContentPageBox({ children, ...props }: ContentPageBoxProps) {
  const location = useLocation();
  const hasSidebar = location.pathname !== '/';

  // Calculate the left offset based on whether the sidebar is present and screen size
  // Use larger negative values to extend width to the left, but keep it at 0 for mobile
  const leftOffset = useBreakpointValue({
    base: "0px",    // No offset on mobile to prevent truncation
    sm: "0px",      // No offset on small tablets
    md: "-24px",    // Medium offset on tablets
    lg: "-96px",    // Larger offset on desktop
  });

  // Calculate content width based on screen size
  const contentWidth = useBreakpointValue({
    base: "100%",               // Full width on mobile
    sm: "100%",                 // Full width on small tablets
    md: "calc(100% + 48px)",    // Medium extension on tablets
    lg: "calc(100% + 120px)",   // Larger extension on desktop
  });

  // Adjust padding based on screen size
  const paddingLeft = useBreakpointValue({
    base: 4,  // Smaller padding on mobile
    sm: 6,    // Small padding on small tablets
    md: 8,    // Medium padding on tablets
    lg: 12,   // Larger padding on desktop
  });

  // Background color with subtle gradient
  const bgColor = useColorModeValue('bg-overlay', 'gray.800');

  return (
    <MotionBox
      as="main"
      id="main-content"
      role="main"
      aria-label="Main content"
      width={contentWidth}
      maxW="none" // Remove max width constraint
      mx={{ base: "auto", md: "0" }} // Center on mobile, align left on larger screens
      ml={{ base: "0", md: leftOffset }} // Set left margin based on offset for larger screens
      pl="0"
      pr={{ base: 2, md: 4 }}
      mt={5} // Reduced top margin
      position="relative"
      left={{ base: "0", md: leftOffset }} // Push content left to align with the edge on larger screens
      overflowX="visible" // Allow content to extend beyond container
      zIndex="1" // Ensure content is above the sidebar
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      <VStack
        bgColor={bgColor}
        gap={{ base: 3, md: 4 }}
        py={{ base: 4, md: 5 }}
        px={{ base: 4, sm: 6, md: 8 }} // Consistent padding on all sides for small screens
        align="stretch"
        width="100%"
        borderRadius={{ base: "md", md: "none" }} // Slight rounding on mobile
        overflow="visible"
        boxShadow="none"
        spacing={{ base: 3, md: 4 }}
      >
        {children}
      </VStack>
    </MotionBox>
  );
}
