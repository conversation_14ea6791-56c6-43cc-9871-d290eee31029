import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SimpleGrid,
  Button,
  ButtonGroup,
  useColorModeValue,
  <PERSON>ing,
  Flex,
  Badge,
  Icon
} from '@chakra-ui/react';
import { FaArrowLeft, FaArrowRight } from 'react-icons/fa';
import { ResumeTemplate } from '../../shared/types';
import { resumeTemplates } from '../data/resumeTemplates';
import ResumeTemplateCard from './ResumeTemplateCard';
import ResumeTemplatePreview from './ResumeTemplatePreview';

interface ResumeTemplateGalleryProps {
  selectedTemplate?: ResumeTemplate;
  onTemplateSelect: (template: ResumeTemplate) => void;
}

type TemplateCategory = 'all' | 'professional' | 'creative' | 'modern' | 'classic';

const CATEGORY_LABELS = {
  all: 'All Templates',
  professional: 'Professional',
  creative: 'Creative',
  modern: 'Modern',
  classic: 'Classic'
} as const;

const ResumeTemplateGallery: React.FC<ResumeTemplateGalleryProps> = ({
  selectedTemplate,
  onTemplateSelect
}) => {
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory>('all');
  const [previewTemplate, setPreviewTemplate] = useState<ResumeTemplate | null>(null);

  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const filteredTemplates = selectedCategory === 'all'
    ? resumeTemplates
    : resumeTemplates.filter(template => template.category === selectedCategory);

  const handleTemplateSelect = (template: ResumeTemplate) => {
    onTemplateSelect(template);
  };

  const handlePreview = (template: ResumeTemplate) => {
    setPreviewTemplate(template);
  };

  const closePreview = () => {
    setPreviewTemplate(null);
  };

  const getCategoryCount = (category: TemplateCategory) => {
    if (category === 'all') return resumeTemplates.length;
    return resumeTemplates.filter(template => template.category === category).length;
  };

  return (
    <VStack spacing={4} align="stretch" width="100%" height="100%">

      {/* Category Filter */}
      <Box>
        <Text fontSize="sm" fontWeight="medium" mb={3} color="gray.600">
          Filter by Category
        </Text>
        <ButtonGroup size="sm" variant="outline" spacing={2} flexWrap="wrap">
          {(Object.keys(CATEGORY_LABELS) as TemplateCategory[]).map((category) => (
            <Button
              key={category}
              onClick={() => setSelectedCategory(category)}
              colorScheme={selectedCategory === category ? 'purple' : 'gray'}
              variant={selectedCategory === category ? 'solid' : 'outline'}
              rightIcon={
                <Badge
                  size="sm"
                  colorScheme={selectedCategory === category ? 'white' : 'gray'}
                  variant="subtle"
                  fontSize="xs"
                  ml={1}
                >
                  {getCategoryCount(category)}
                </Badge>
              }
            >
              {CATEGORY_LABELS[category]}
            </Button>
          ))}
        </ButtonGroup>
      </Box>

      {/* Templates Grid */}
      <Box flex="1" overflowY="auto" minHeight="300px">
        <SimpleGrid
          columns={{ base: 1, md: 2, lg: 3 }}
          spacing={4}
          width="100%"
          pb={4}
        >
          {filteredTemplates.map((template) => (
            <ResumeTemplateCard
              key={template.id}
              template={template}
              isSelected={selectedTemplate?.id === template.id}
              onSelect={handleTemplateSelect}
              onPreview={handlePreview}
            />
          ))}
        </SimpleGrid>

        {filteredTemplates.length === 0 && (
          <Box
            textAlign="center"
            py={12}
            border="2px dashed"
            borderColor={borderColor}
            borderRadius="lg"
          >
            <Text color="gray.500" fontSize="lg">
              No templates found in this category
            </Text>
          </Box>
        )}
      </Box>



      {/* Template Preview Modal */}
      {previewTemplate && (
        <ResumeTemplatePreview
          template={previewTemplate}
          isOpen={!!previewTemplate}
          onClose={closePreview}
          onSelect={() => {
            handleTemplateSelect(previewTemplate);
            closePreview();
          }}
          isSelected={selectedTemplate?.id === previewTemplate.id}
        />
      )}
    </VStack>
  );
};

export default ResumeTemplateGallery;
