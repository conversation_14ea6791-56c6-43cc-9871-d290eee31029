import React from 'react';
import {
  Button,
  ButtonProps,
  Icon,
  Text,
  useColorModeValue,
  Box,
} from '@chakra-ui/react';
import { IconType } from 'react-icons';
import { motion } from 'framer-motion';
import { touchTargetProps, focusRingStyles } from '../utils/accessibility';
import { useReducedMotion } from '../hooks/useReducedMotion';

// Create motion components
const MotionButton = motion(Button);

interface ActionButtonProps extends ButtonProps {
  icon?: IconType;
  label: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
}

/**
 * Modern action button with icon and label
 * Designed with Apple-inspired aesthetics
 */
const ActionButton: React.FC<ActionButtonProps> = ({
  icon,
  label,
  variant = 'primary',
  ...rest
}) => {
  const prefersReducedMotion = useReducedMotion();
  // Define consistent color schemes based on variant
  const getColorScheme = () => {
    switch (variant) {
      case 'primary':
        return 'blue';
      case 'secondary':
        return 'cyan';
      case 'outline':
      case 'ghost':
        return 'gray';
      default:
        return 'blue';
    }
  };

  // Define button variant
  const getVariant = () => {
    switch (variant) {
      case 'primary':
      case 'secondary':
        return 'solid';
      case 'outline':
        return 'outline';
      case 'ghost':
        return 'ghost';
      default:
        return 'solid';
    }
  };

  // Define consistent background colors
  const getBgColor = () => {
    if (variant === 'primary') {
      return useColorModeValue('blue.500', 'blue.400');
    } else if (variant === 'secondary') {
      return useColorModeValue('cyan.500', 'cyan.400');
    }
    return undefined;
  };

  // Define hover effect
  const hoverBg = useColorModeValue(
    variant === 'primary' ? 'blue.600' :
    variant === 'secondary' ? 'cyan.600' :
    variant === 'outline' ? 'rgba(66, 153, 225, 0.08)' : 'rgba(66, 153, 225, 0.08)',

    variant === 'primary' ? 'blue.500' :
    variant === 'secondary' ? 'cyan.500' :
    variant === 'outline' ? 'rgba(66, 153, 225, 0.16)' : 'rgba(66, 153, 225, 0.16)'
  );

  // Define consistent border styles
  const getBorderStyle = () => {
    if (variant === 'outline') {
      return {
        borderWidth: '1px',
        borderColor: useColorModeValue('gray.300', 'gray.600'),
      };
    }
    return {};
  };

  // Get text color
  const textColor = useColorModeValue(
    variant === 'primary' || variant === 'secondary' ? 'white' : 'inherit',
    variant === 'primary' || variant === 'secondary' ? 'white' : 'inherit'
  );

  const bgColor = getBgColor();
  const borderStyle = getBorderStyle();

  return (
    <MotionButton
      colorScheme={getColorScheme()}
      variant={getVariant()}
      size="sm"
      px={4}
      py={1}
      height="28px"
      minH="28px"
      minW="auto"
      borderRadius="full"
      fontWeight="normal"
      fontSize="xs"
      letterSpacing="0.2px"
      whiteSpace="nowrap"
      bg={bgColor}
      color={textColor}
      boxShadow="none"
      transition="all 0.2s"
      whileHover={prefersReducedMotion ? {} : { opacity: 0.9 }}
      whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
      _hover={{
        bg: hoverBg,
        transform: 'none', // Handled by framer-motion
      }}
      _active={{
        transform: 'none', // Handled by framer-motion
      }}
      {...focusRingStyles}
      {...touchTargetProps}
      {...borderStyle}
      {...rest}
    >
      {icon && (
        <Icon
          as={icon}
          mr={label ? 1.5 : 0}
          boxSize={label ? 3 : 3.5}
          opacity={0.9}
        />
      )}
      {label && <Text fontSize="xs" whiteSpace="nowrap">{label}</Text>}
    </MotionButton>
  );
};

export default ActionButton;
