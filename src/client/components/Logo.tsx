import { Box, Text, HStack, VStack, useColorModeValue } from '@chakra-ui/react';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);
const MotionPath = motion.path;

export default function Logo() {
  const textColor = useColorModeValue('gray.800', 'white');
  const primaryColor = useColorModeValue('blue.600', 'blue.300');
  const secondaryColor = useColorModeValue('cyan.600', 'cyan.400');
  const dartGradient = useColorModeValue(
    'linear(135deg, blue.500 0%, cyan.600 100%)',
    'linear(135deg, blue.400 0%, cyan.500 100%)'
  );
  const pathColor = useColorModeValue('blue.200', 'blue.600');
  const glowColor = useColorModeValue('blue.400', 'blue.300');

  return (
    <VStack spacing={0} alignItems="flex-start">
      <HStack spacing={3} alignItems="center">
        {/* Career Dart Logo */}
        <MotionBox
          position="relative"
          width="40px"
          height="40px"
          initial={{ scale: 1 }}
          animate={{
            scale: [1, 1.02, 1],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {/* Background Circle */}
          <Box
            position="absolute"
            width="40px"
            height="40px"
            borderRadius="50%"
            bgGradient={dartGradient}
            boxShadow={`0 0 20px ${glowColor}25, 0 4px 15px rgba(0,0,0,0.1)`}
            border="2px solid"
            borderColor="white"
            _dark={{ borderColor: "gray.700" }}
          />

          {/* Career Trajectory Path */}
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            width="32px"
            height="32px"
          >
            <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              {/* Trajectory Path */}
              <MotionPath
                d="M6 26 Q16 16 26 6"
                stroke={pathColor}
                strokeWidth="2"
                fill="none"
                strokeLinecap="round"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{
                  pathLength: [0, 1, 1, 0],
                  opacity: [0, 0.6, 0.6, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
              />

              {/* Dart Arrow */}
              <MotionBox
                as="g"
                animate={{
                  x: [0, 1, 0],
                  y: [0, -1, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                {/* Dart Body */}
                <path
                  d="M20 12 L26 6 L24 4 L18 10 L16 8 L14 10 L20 16 L22 14 L20 12 Z"
                  fill="white"
                  stroke="rgba(255,255,255,0.3)"
                  strokeWidth="0.5"
                />
                {/* Dart Tip */}
                <circle
                  cx="25"
                  cy="7"
                  r="1.5"
                  fill="white"
                />
                {/* Dart Fletching */}
                <path
                  d="M14 10 L12 8 L14 12 L16 10 Z"
                  fill="rgba(255,255,255,0.8)"
                />
              </MotionBox>

              {/* Progress Dots */}
              <MotionBox
                as="circle"
                cx="10"
                cy="22"
                r="1.5"
                fill="white"
                animate={{
                  opacity: [0.3, 1, 0.3],
                  scale: [0.8, 1.2, 0.8],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.5
                }}
              />
              <MotionBox
                as="circle"
                cx="16"
                cy="16"
                r="1"
                fill="white"
                animate={{
                  opacity: [0.3, 1, 0.3],
                  scale: [0.8, 1.2, 0.8],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
              />
              <MotionBox
                as="circle"
                cx="22"
                cy="10"
                r="0.8"
                fill="white"
                animate={{
                  opacity: [0.3, 1, 0.3],
                  scale: [0.8, 1.2, 0.8],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1.5
                }}
              />
            </svg>
          </Box>
        </MotionBox>

        {/* Brand Text */}
        <Box>
          <Text
            fontSize="xl"
            fontWeight="black"
            color={textColor}
            letterSpacing="tight"
            lineHeight="1"
          >
            Career
            <Text
              as="span"
              color={primaryColor}
              fontWeight="black"
              position="relative"
              ml="1px"
            >
              Dart
              {/* Dynamic underline accent */}
              <MotionBox
                position="absolute"
                bottom="-2px"
                left="0"
                right="0"
                height="2px"
                bgGradient="linear(to-r, transparent, blue.400, cyan.500, transparent)"
                borderRadius="full"
                animate={{
                  scaleX: [0.5, 1, 0.5],
                  opacity: [0.6, 1, 0.6],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
            </Text>
          </Text>
        </Box>
      </HStack>

      {/* Tagline */}
      <MotionBox
        ml="52px" // Align with the text, accounting for larger icon
        mt="2px"
        animate={{
          opacity: [0.7, 1, 0.7],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        <Text
          fontSize="xs"
          fontWeight="semibold"
          bgGradient="linear(to-r, gray.500, blue.500, cyan.600)"
          bgClip="text"
          _dark={{
            bgGradient: "linear(to-r, gray.400, blue.300, cyan.400)",
            bgClip: "text"
          }}
          letterSpacing="wide"
          textTransform="uppercase"
        >
          Your Personal Career Companion
        </Text>
      </MotionBox>
    </VStack>
  );
}