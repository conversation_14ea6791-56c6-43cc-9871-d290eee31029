import { type Job, type User } from "wasp/entities";

import {
  useAction,
  type OptimisticUpdateDefinition,
  updateJob,
  createJob,
  useQuery,
  getJobs,
  getCoverLetters,
  createJobApplication,
  getJobApplications,
} from "wasp/client/operations";
import { useAuth } from "wasp/client/auth";

import { useState, useEffect, useRef } from 'react';
import {
  Heading,
  Spacer,
  VStack,
  HStack,
  Button,
  Text,
  Box,
  Badge,
  Grid,
  GridItem,
  SimpleGrid,
  useDisclosure,
  Divider,
  Checkbox,
  Spinner,
  Tooltip,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  Select,
  useToast,
  IconButton,
} from '@chakra-ui/react';
import ModalElement from './components/Modal';
import ContentPageBox from './components/ContentPageBox';
import { useNavigate } from 'react-router-dom';
import { DeleteJob } from './components/AlertDialog';
import { FiDelete } from 'react-icons/fi';
import { FaPlus, FaBriefcase, FaLinkedin, FaEdit, FaCopy, FaEye, FaSave, FaTimes, FaCheck } from 'react-icons/fa';
import PageHeader from './components/PageHeader';
import ActionButton from './components/ActionButton';
import ContentContainer from './components/ContentContainer';
import LinkedInJobImport from './components/LinkedInJobImport';
import { EmptyJobs } from './components/EmptyState';
import { SimpleLoading } from './components/LoadingState';
import { GeneralError } from './components/ErrorState';

function JobsPage({ user }: { user: User }) {
  const [jobId, setJobId] = useState<string>('');
  const [isImportingJob, setIsImportingJob] = useState<boolean>(false);
  const [isCreatingJob, setIsCreatingJob] = useState<boolean>(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [editingJobId, setEditingJobId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState({
    title: '',
    company: '',
    location: '',
    description: '',
    isCompleted: false,
    status: 'Saved'
  });
  const [createFormData, setCreateFormData] = useState({
    title: '',
    company: '',
    location: '',
    description: ''
  });

  const toast = useToast();
  const navigate = useNavigate();
  const { data: authUser } = useAuth();

  // Fetch data only if user is authenticated
  const { data: jobs, isLoading, error } = useQuery(getJobs, undefined, { enabled: !!authUser });
  const { data: coverLetter } = useQuery(getCoverLetters, { id: jobId }, { enabled: jobId.length > 0 && !!authUser });
  const { data: jobApplications } = useQuery(getJobApplications, undefined, { enabled: !!authUser });

  useEffect(() => {
    if (user.subscriptionStatus === 'past_due') {
      navigate('/profile');
    }
  }, [user.subscriptionStatus]);

  // Set the first job as selected when data loads
  useEffect(() => {
    if (jobs && jobs.length > 0 && !selectedJob) {
      setSelectedJob(jobs[0]);
    }
  }, [jobs]);

  const updateJobOptimistically = useAction(updateJob, {
    optimisticUpdates: [
      {
        getQuerySpecifier: () => [getJobs],
        updateQuery: ({ isCompleted, id }, oldData) => {
          return oldData && oldData.map((job) => (job.id === id ? { ...job, isCompleted } : job));
        },
      } as OptimisticUpdateDefinition<Pick<Job, 'id' | 'isCompleted'>, Job[]>,
    ],
  });

  const createJobApplicationAction = useAction(createJobApplication);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: deleteIsOpen, onOpen: deleteOnOpen, onClose: deleteOnClose } = useDisclosure();

  const coverLetterHandler = (job: Job) => {
    if (job) {
      setJobId(job.id);
      onOpen();
    }
  };

  const checkboxHandler = async (e: any, job: Job) => {
    try {
      const isCompleted = e.target.checked;

      // Update the job
      const payload = {
        id: job.id,
        title: job.title,
        company: job.company,
        location: job.location,
        description: job.description,
        isCompleted: isCompleted,
      };
      updateJobOptimistically(payload);

      // If marking as completed (applied), create a job application
      if (isCompleted) {
        try {
          // Check if there's already an application for this job
          const existingApplication = jobApplications?.find(app => app.jobId === job.id);

          if (!existingApplication) {
            // Create a new job application
            await createJobApplicationAction({
              jobId: job.id,
              dateApplied: new Date().toISOString(),
              status: 'Applied',
              notes: 'Marked as applied from Jobs page',
            });

            toast({
              title: 'Job application created',
              description: `Job added to tracker with status: Applied`,
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }
        } catch (error) {
          console.error('Error creating job application:', error);
          toast({
            title: 'Error',
            description: 'Failed to update job application status.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'Failed to update job status.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const updateCoverLetterHandler = async (jobId: string) => {
    navigate(`/?job=${jobId}`);
  };

  const handleJobImport = async (jobData: any) => {
    try {
      console.log('Imported job data:', jobData);

      // Populate the creation form with imported data
      setCreateFormData({
        title: jobData.title || '',
        company: jobData.company || '',
        location: jobData.location || '',
        description: jobData.description || ''
      });

      // Hide the import form and show the creation form
      setIsImportingJob(false);
      setIsCreatingJob(true);

      // Show success message
      toast({
        title: 'Job data imported',
        description: `Successfully imported job details for ${jobData.title} at ${jobData.company}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error handling LinkedIn job import:', error);
      toast({
        title: 'Import failed',
        description: 'Failed to import job data. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handlePreviewJob = (job: Job) => {
    setSelectedJob(job);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  // Get job application status for a job
  const getJobApplicationStatus = (jobId: string) => {
    if (!jobApplications) return null;

    const application = jobApplications.find(app => app.jobId === jobId);
    return application ? application.status : null;
  };

  // Get color for job application status
  const getStatusColor = (status: string | null) => {
    if (!status) return "gray";

    switch (status) {
      case "Applied": return "blue";
      case "Interview": return "orange";
      case "Offer": return "green";
      case "Rejected": return "red";
      default: return "gray";
    }
  };

  // Start editing a job
  const handleEditJob = (job: Job) => {
    setEditingJobId(job.id);
    setEditFormData({
      title: job.title,
      company: job.company,
      location: job.location,
      description: job.description,
      isCompleted: job.isCompleted,
      status: job.isCompleted ? 'Applied' : 'Saved'
    });
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingJobId(null);
  };

  // Handle form field changes for editing
  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form field changes for creation
  const handleCreateFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCreateFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: checked,
      // If marking as completed, set status to Applied
      ...(name === 'isCompleted' && checked ? { status: 'Applied' } : {})
    }));
  };

  // Start creating a new job
  const handleCreateJob = () => {
    setCreateFormData({
      title: '',
      company: '',
      location: '',
      description: ''
    });
    setIsCreatingJob(true);
    setIsImportingJob(false);
  };

  // Cancel job creation
  const handleCancelCreate = () => {
    setIsCreatingJob(false);
    setCreateFormData({
      title: '',
      company: '',
      location: '',
      description: ''
    });
  };

  // Save new job
  const handleSaveNewJob = async () => {
    try {
      // Validate required fields
      if (!createFormData.title.trim() || !createFormData.company.trim() || !createFormData.description.trim()) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields (Title, Company, and Description).',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      const newJob = await createJob({
        title: createFormData.title,
        company: createFormData.company,
        location: createFormData.location,
        description: createFormData.description
      });

      // Reset creation form and hide it
      setIsCreatingJob(false);
      setCreateFormData({
        title: '',
        company: '',
        location: '',
        description: ''
      });

      // Set the newly created job as selected
      setSelectedJob(newJob);

      toast({
        title: 'Job created',
        description: 'The job has been created successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error creating job:', error);
      toast({
        title: 'Error',
        description: 'Failed to create job. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Save job edits
  const handleSaveJob = async (jobId: string) => {
    try {
      await updateJobOptimistically({
        id: jobId,
        title: editFormData.title,
        company: editFormData.company,
        location: editFormData.location,
        description: editFormData.description,
        isCompleted: editFormData.isCompleted
      });

      // If the job status has changed, create or update a job application
      if (editFormData.status !== 'Saved') {
        try {
          // Check if there's already an application for this job
          const existingApplication = jobApplications?.find(app => app.jobId === jobId);

          if (!existingApplication) {
            // Create a new job application
            await createJobApplicationAction({
              jobId: jobId,
              dateApplied: new Date().toISOString(),
              status: editFormData.status,
              notes: `Status changed to ${editFormData.status}`,
            });

            toast({
              title: 'Job application created',
              description: `Job added to tracker with status: ${editFormData.status}`,
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }
        } catch (error) {
          console.error('Error creating job application:', error);
          toast({
            title: 'Error',
            description: 'Failed to update job application status.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      }

      setEditingJobId(null);

      // Update the selected job if it's the one being edited
      if (selectedJob && selectedJob.id === jobId) {
        setSelectedJob({
          ...selectedJob,
          title: editFormData.title,
          company: editFormData.company,
          location: editFormData.location,
          description: editFormData.description,
          isCompleted: editFormData.isCompleted
        });
      }

      toast({
        title: 'Job updated',
        description: 'The job has been updated successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error updating job:', error);
      toast({
        title: 'Error',
        description: 'Failed to update job. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return (
    <>
      <ContentPageBox>
        <PageHeader
          title="Your Jobs"
          subtitle="Track and manage your job applications"
        >
          <HStack spacing={3}>
            <ActionButton
              icon={FaLinkedin}
              label="Import from LinkedIn"
              variant="outline"
              onClick={() => {
                setIsImportingJob(!isImportingJob);
                setIsCreatingJob(false);
              }}
            />
            <ActionButton
              icon={FaPlus}
              label="Create New"
              variant="primary"
              onClick={handleCreateJob}
            />
          </HStack>
        </PageHeader>
        {isImportingJob && (
          <ContentContainer delay={0.2}>
            <LinkedInJobImport
              onJobImported={handleJobImport}
              isCollapsible={false}
            />
          </ContentContainer>
        )}

        {isCreatingJob && (
          <ContentContainer delay={0.2}>
            <Box bg="white" p={6} borderRadius="lg" border="1px" borderColor="gray.200">
              <VStack align="stretch" spacing={4}>
                <HStack justify="space-between" align="center">
                  <Heading size="md" color="gray.700">Create New Job</Heading>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleCancelCreate}
                  >
                    Cancel
                  </Button>
                </HStack>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                  <FormControl isRequired>
                    <FormLabel fontSize="sm" fontWeight="medium">Job Title</FormLabel>
                    <Input
                      name="title"
                      value={createFormData.title}
                      onChange={handleCreateFormChange}
                      placeholder="e.g., Senior Software Engineer"
                      size="md"
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel fontSize="sm" fontWeight="medium">Company</FormLabel>
                    <Input
                      name="company"
                      value={createFormData.company}
                      onChange={handleCreateFormChange}
                      placeholder="e.g., Google"
                      size="md"
                    />
                  </FormControl>
                </SimpleGrid>

                <FormControl>
                  <FormLabel fontSize="sm" fontWeight="medium">Location</FormLabel>
                  <Input
                    name="location"
                    value={createFormData.location}
                    onChange={handleCreateFormChange}
                    placeholder="e.g., San Francisco, CA"
                    size="md"
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel fontSize="sm" fontWeight="medium">Job Description</FormLabel>
                  <Textarea
                    name="description"
                    value={createFormData.description}
                    onChange={handleCreateFormChange}
                    placeholder="Paste the job description here..."
                    minH="200px"
                    resize="vertical"
                  />
                </FormControl>

                <HStack justify="flex-end" spacing={3} pt={2}>
                  <Button
                    variant="outline"
                    onClick={handleCancelCreate}
                  >
                    Cancel
                  </Button>
                  <Button
                    colorScheme="purple"
                    onClick={handleSaveNewJob}
                    isDisabled={!createFormData.title.trim() || !createFormData.company.trim() || !createFormData.description.trim()}
                  >
                    Create Job
                  </Button>
                </HStack>
              </VStack>
            </Box>
          </ContentContainer>
        )}

        {isLoading ? (
          <SimpleLoading message="Loading your jobs..." />
        ) : error ? (
          <GeneralError
            error={error}
            onRetry={() => window.location.reload()}
            showErrorDetails={false}
          />
        ) : !jobs || jobs.length === 0 ? (
          <EmptyJobs
            primaryAction={{
              label: 'Create New Job',
              onClick: () => setIsImportingJob(true),
              icon: <FaPlus />,
              colorScheme: 'blue',
            }}
            secondaryAction={{
              label: 'Import from LinkedIn',
              onClick: () => setIsImportingJob(true),
              icon: <FaLinkedin />,
              variant: 'outline',
            }}
          />
        ) : (
          <Grid templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }} gap={6}>
            {/* Left side - List of jobs */}
            <GridItem>
              <ContentContainer delay={0.3} maxH="calc(100vh - 200px)" overflowY="auto">
                <VStack spacing={2} align="stretch">
                  {jobs.map((job) => (
                    <Box
                      key={job.id}
                      p={3}
                      borderRadius="md"
                      borderLeft={job.id === selectedJob?.id ? '2px solid' : 'none'}
                      borderColor={job.id === selectedJob?.id ? 'purple.400' : 'transparent'}
                      bg={job.id === selectedJob?.id ? 'gray.50' : 'transparent'}
                      cursor={editingJobId === job.id ? 'default' : 'pointer'}
                      onClick={() => editingJobId !== job.id && handlePreviewJob(job)}
                      _hover={{ bg: 'gray.50' }}
                      transition="all 0.15s"
                      boxShadow="none"
                      mb={1}
                    >
                      {editingJobId === job.id ? (
                        // Edit mode
                        <VStack align="stretch" spacing={3} onClick={(e) => e.stopPropagation()}>
                          <FormControl>
                            <FormLabel fontSize="xs">Job Title</FormLabel>
                            <Input
                              name="title"
                              value={editFormData.title}
                              onChange={handleEditFormChange}
                              size="sm"
                            />
                          </FormControl>

                          <HStack spacing={3}>
                            <FormControl>
                              <FormLabel fontSize="xs">Company</FormLabel>
                              <Input
                                name="company"
                                value={editFormData.company}
                                onChange={handleEditFormChange}
                                size="sm"
                              />
                            </FormControl>

                            <FormControl>
                              <FormLabel fontSize="xs">Location</FormLabel>
                              <Input
                                name="location"
                                value={editFormData.location}
                                onChange={handleEditFormChange}
                                size="sm"
                              />
                            </FormControl>
                          </HStack>

                          <FormControl>
                            <FormLabel fontSize="xs">Status</FormLabel>
                            <Select
                              name="status"
                              value={editFormData.status}
                              onChange={handleEditFormChange}
                              size="sm"
                            >
                              <option value="Saved">Saved</option>
                              <option value="Applied">Applied</option>
                              <option value="Interview">Interview</option>
                              <option value="Offer">Offer</option>
                              <option value="Rejected">Rejected</option>
                            </Select>
                          </FormControl>

                          <HStack justify="flex-end" spacing={2} mt={2}>
                            <IconButton
                              aria-label="Cancel"
                              icon={<FaTimes />}
                              size="sm"
                              variant="ghost"
                              onClick={handleCancelEdit}
                            />
                            <IconButton
                              aria-label="Save"
                              icon={<FaSave />}
                              size="sm"
                              colorScheme="green"
                              onClick={() => handleSaveJob(job.id)}
                            />
                          </HStack>
                        </VStack>
                      ) : (
                        // View mode
                        <VStack align="stretch" spacing={2}>
                          <HStack justify="space-between" align="flex-start">
                            <Heading
                              size="sm"
                              fontWeight="medium"
                              textDecoration={job.isCompleted ? 'line-through' : 'none'}
                            >
                              {job.title}
                            </Heading>
                            <HStack spacing={1}>
                              {getJobApplicationStatus(job.id) && (
                                <Badge colorScheme={getStatusColor(getJobApplicationStatus(job.id))} fontSize="xs" variant="subtle">
                                  {getJobApplicationStatus(job.id)}
                                </Badge>
                              )}
                              <Badge colorScheme={job.isCompleted ? "green" : "purple"} fontSize="xs" variant="subtle">
                                {job.isCompleted ? "Applied" : formatDate(job.createdAt)}
                              </Badge>
                            </HStack>
                          </HStack>
                          <Text noOfLines={1} fontSize="sm" color="gray.600">
                            {job.company} • {job.location}
                          </Text>
                          <HStack spacing={2} justify="flex-end">
                            <Tooltip label="Mark as Applied">
                              <span>
                                <Checkbox
                                  isChecked={job.isCompleted}
                                  onChange={(e) => {
                                    e.stopPropagation();
                                    checkboxHandler(e, job);
                                  }}
                                  colorScheme="green"
                                  size="sm"
                                />
                              </span>
                            </Tooltip>
                            <Tooltip label="Edit Job">
                              <span>
                                <ActionButton
                                  icon={FaEdit}
                                  label=""
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditJob(job);
                                  }}
                                />
                              </span>
                            </Tooltip>
                            <Tooltip label="Create Cover Letter">
                              <span>
                                <ActionButton
                                  icon={FaPlus}
                                  label=""
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    updateCoverLetterHandler(job.id);
                                  }}
                                />
                              </span>
                            </Tooltip>
                            <Tooltip label="Delete Job">
                              <span>
                                <ActionButton
                                  icon={FiDelete}
                                  label=""
                                  variant="ghost"
                                  colorScheme="red"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setJobId(job.id);
                                    deleteOnOpen();
                                  }}
                                />
                              </span>
                            </Tooltip>
                          </HStack>
                        </VStack>
                      )}
                    </Box>
                  ))}
                </VStack>
              </ContentContainer>
            </GridItem>

            {/* Right side - Job details preview */}
            <GridItem>
              {selectedJob ? (
                <ContentContainer
                  height="calc(100vh - 200px)"
                  overflowY="auto"
                  delay={0.4}
                >
                  <VStack align="stretch" spacing={6}>
                    <Box>
                      {editingJobId === selectedJob.id ? (
                        // Edit mode for job details
                        <VStack align="stretch" spacing={4}>
                          <HStack justify="space-between">
                            <Heading size="md" fontWeight="medium">Edit Job</Heading>
                            <HStack>
                              <IconButton
                                aria-label="Cancel"
                                icon={<FaTimes />}
                                size="sm"
                                variant="ghost"
                                onClick={handleCancelEdit}
                              />
                              <IconButton
                                aria-label="Save"
                                icon={<FaSave />}
                                size="sm"
                                colorScheme="green"
                                onClick={() => handleSaveJob(selectedJob.id)}
                              />
                            </HStack>
                          </HStack>

                          <FormControl>
                            <FormLabel>Job Title</FormLabel>
                            <Input
                              name="title"
                              value={editFormData.title}
                              onChange={handleEditFormChange}
                            />
                          </FormControl>

                          <FormControl>
                            <FormLabel>Company</FormLabel>
                            <Input
                              name="company"
                              value={editFormData.company}
                              onChange={handleEditFormChange}
                            />
                          </FormControl>

                          <FormControl>
                            <FormLabel>Location</FormLabel>
                            <Input
                              name="location"
                              value={editFormData.location}
                              onChange={handleEditFormChange}
                            />
                          </FormControl>

                          <FormControl>
                            <FormLabel>Status</FormLabel>
                            <Select
                              name="status"
                              value={editFormData.status}
                              onChange={handleEditFormChange}
                            >
                              <option value="Saved">Saved</option>
                              <option value="Applied">Applied</option>
                              <option value="Interview">Interview</option>
                              <option value="Offer">Offer</option>
                              <option value="Rejected">Rejected</option>
                            </Select>
                          </FormControl>

                          <FormControl>
                            <FormLabel>Job Description</FormLabel>
                            <Textarea
                              name="description"
                              value={editFormData.description}
                              onChange={handleEditFormChange}
                              minH="200px"
                            />
                          </FormControl>

                          <FormControl display="flex" alignItems="center">
                            <Checkbox
                              name="isCompleted"
                              isChecked={editFormData.isCompleted}
                              onChange={handleCheckboxChange}
                              colorScheme="green"
                              size="lg"
                            />
                            <FormLabel mb="0" ml={2}>
                              Mark as Applied
                            </FormLabel>
                          </FormControl>
                        </VStack>
                      ) : (
                        // View mode for job details
                        <>
                          <HStack justify="space-between" mb={4}>
                            <Heading size="md" fontWeight="medium">{selectedJob.title}</Heading>
                            <HStack>
                              <Badge colorScheme="purple" px={2} py={1}>
                                {selectedJob.company}
                              </Badge>
                              <IconButton
                                aria-label="Edit Job"
                                icon={<FaEdit />}
                                size="sm"
                                variant="ghost"
                                onClick={() => handleEditJob(selectedJob)}
                              />
                            </HStack>
                          </HStack>

                          <HStack spacing={4} mb={6}>
                            <Badge colorScheme="blue" variant="subtle">
                              {selectedJob.location}
                            </Badge>
                            <Badge colorScheme={selectedJob.isCompleted ? "green" : "gray"} variant="subtle">
                              {selectedJob.isCompleted ? "Applied" : "Not Applied"}
                            </Badge>
                            {getJobApplicationStatus(selectedJob.id) && (
                              <Badge colorScheme={getStatusColor(getJobApplicationStatus(selectedJob.id))} variant="subtle">
                                Status: {getJobApplicationStatus(selectedJob.id)}
                              </Badge>
                            )}
                            <Text fontSize="sm" color="gray.500">
                              Added: {formatDate(selectedJob.createdAt)}
                            </Text>
                          </HStack>

                          <Heading size="sm" fontWeight="medium" mb={3}>Job Description</Heading>
                          <Text whiteSpace="pre-wrap" fontSize="sm">{selectedJob.description}</Text>

                          <Divider my={6} />

                          <HStack spacing={3} justify="flex-end">
                            <ActionButton
                              icon={FaEye}
                              label="View Letters"
                              variant="outline"
                              onClick={() => coverLetterHandler(selectedJob)}
                            />
                            <ActionButton
                              icon={FaPlus}
                              label="Create Letter"
                              variant="primary"
                              onClick={() => updateCoverLetterHandler(selectedJob.id)}
                            />
                          </HStack>
                        </>
                      )}
                    </Box>
                  </VStack>
                </ContentContainer>
              ) : (
                <ContentContainer
                  height="calc(100vh - 200px)"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  delay={0.4}
                >
                  <Text fontSize="sm" color="gray.500">
                    Select a job from the list to view details
                  </Text>
                </ContentContainer>
              )}
            </GridItem>
          </Grid>
        )}
      </ContentPageBox>
      {coverLetter && coverLetter.length > 0 && (
        <ModalElement coverLetterData={coverLetter} isOpen={isOpen} onOpen={onOpen} onClose={onClose} />
      )}
      <DeleteJob jobId={jobId} isOpen={deleteIsOpen} onOpen={deleteOnOpen} onClose={deleteOnClose} />
    </>
  );
}

export default JobsPage;
