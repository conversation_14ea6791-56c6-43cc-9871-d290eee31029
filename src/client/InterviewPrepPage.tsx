import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Input,
  Textarea,
  Button,
  Tag,
  TagLabel,
  TagCloseButton,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Grid,
  GridItem,
  List,
  ListItem,
  ListIcon,
  Divider,
  Select,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Spinner,
  Badge,
  useToast,
  FormControl,
  FormLabel,
  Icon
} from '@chakra-ui/react';
import { FaSearch, FaBookmark, FaCheck, FaLightbulb, FaRegLightbulb, FaBriefcase, FaRobot, FaTrash, FaTimes } from 'react-icons/fa';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ContentContainer from './components/ContentContainer';
import ActionButton from './components/ActionButton';
import { useQuery, useAction } from 'wasp/client/operations';
import { EmptyInterviewQuestions, EmptyJobs } from './components/EmptyState';
import { SimpleLoading } from './components/LoadingState';
import {
  getJobs,
  generateInterviewQuestions,
  getInterviewQuestionSets,
  getSavedInterviewQuestions,
  getPracticeAnswers,
  saveInterviewQuestion,
  removeSavedInterviewQuestion,
  savePracticeAnswer,
  deletePracticeAnswer
} from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';
import UpgradeModal from './components/UpgradeModal';
import CreditsIndicator from './components/CreditsIndicator';

// Sample interview questions data
const commonQuestions = [
  {
    id: 1,
    category: 'Behavioral',
    question: 'Tell me about a time when you faced a challenging situation at work.',
    tips: 'Use the STAR method: Situation, Task, Action, Result.'
  },
  {
    id: 2,
    category: 'Behavioral',
    question: 'Describe a situation where you had to work with a difficult team member.',
    tips: 'Focus on how you handled the situation professionally.'
  },
  {
    id: 3,
    category: 'Technical',
    question: 'Explain your approach to problem-solving.',
    tips: 'Outline your step-by-step methodology with a real example.'
  },
  {
    id: 4,
    category: 'Technical',
    question: 'How do you stay updated with the latest industry trends?',
    tips: 'Mention specific resources, communities, or practices.'
  },
  {
    id: 5,
    category: 'Career Goals',
    question: 'Where do you see yourself in 5 years?',
    tips: 'Be realistic but ambitious, focusing on skill development and growth.'
  }
];

// Interface for generated interview questions
interface GeneratedQuestion {
  category: string;
  question: string;
  tips: string;
  answer?: string; // Optional answer field
  references?: string; // Optional references field
}

interface GeneratedQuestionSet {
  id?: string; // Client-side ID for tracking
  jobId: string;
  jobTitle: string;
  company: string;
  jobDescription?: string; // Add job description
  questions: GeneratedQuestion[];
  generatedAt: Date;
}

// Interface for practice items
interface PracticeItem {
  id: string;
  type: 'common' | 'generated';
  question: string;
  category: string;
  tips: string;
  answer?: string;
  references?: string;
  userAnswer?: string; // Make userAnswer optional
  savedAt: Date;
  questionId?: number; // For common questions
  questionObj?: GeneratedQuestion; // For generated questions
}

export default function InterviewPrepPage() {
  const { data: user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('All');
  const [practiceAnswer, setPracticeAnswer] = useState('');
  const [selectedQuestion, setSelectedQuestion] = useState<number | null>(null);
  const [selectedJobId, setSelectedJobId] = useState<string>('');
  const [selectedQuestionSet, setSelectedQuestionSet] = useState<any | null>(null);
  const [selectedGeneratedQuestion, setSelectedGeneratedQuestion] = useState<GeneratedQuestion | null>(null);
  const [generatedPracticeAnswer, setGeneratedPracticeAnswer] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  // Server-side data queries - SECURE: User-specific data only
  const {
    data: questionSets,
    isLoading: isLoadingQuestionSets,
    refetch: refetchQuestionSets
  } = useQuery(getInterviewQuestionSets, undefined, { enabled: !!user });

  const {
    data: savedQuestions,
    isLoading: isLoadingSavedQuestions,
    refetch: refetchSavedQuestions
  } = useQuery(getSavedInterviewQuestions, undefined, { enabled: !!user });

  const {
    data: practiceAnswers,
    isLoading: isLoadingPracticeAnswers,
    refetch: refetchPracticeAnswers
  } = useQuery(getPracticeAnswers, undefined, { enabled: !!user });

  // Server-side actions - SECURE: User authentication enforced
  const saveQuestionAction = useAction(saveInterviewQuestion);
  const removeSavedQuestionAction = useAction(removeSavedInterviewQuestion);
  const savePracticeAnswerAction = useAction(savePracticeAnswer);
  const deletePracticeAnswerAction = useAction(deletePracticeAnswer);

  // Set the first question set as selected if none is selected and sets are available
  useEffect(() => {
    if (questionSets && questionSets.length > 0 && !selectedQuestionSet) {
      setSelectedQuestionSet(questionSets[0]);
    }
  }, [questionSets, selectedQuestionSet]);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: upgradeIsOpen, onOpen: upgradeOnOpen, onClose: upgradeOnClose } = useDisclosure();
  const toast = useToast();

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const accentColor = useColorModeValue('purple.500', 'purple.300');

  // Fetch jobs only if user is authenticated
  const { data: jobs, isLoading: isLoadingJobs } = useQuery(getJobs, undefined, { enabled: !!user });

  // Generate interview questions action
  const generateQuestions = useAction(generateInterviewQuestions);

  // Filter common questions based on search query and category
  const filteredCommonQuestions = useMemo(() => {
    let filtered = commonQuestions;

    // Apply category filter if not "All"
    if (categoryFilter !== 'All') {
      filtered = filtered.filter(q => q.category === categoryFilter);
    }

    // Apply search query if present
    if (searchQuery) {
      filtered = filtered.filter(q =>
        q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        q.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        q.tips.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [commonQuestions, searchQuery, categoryFilter]);

  // Filter generated questions based on search query and category
  const filteredGeneratedQuestions = useMemo(() => {
    if (!selectedQuestionSet) return [];

    let filtered = selectedQuestionSet.questions;

    // Apply category filter if not "All"
    if (categoryFilter !== 'All') {
      filtered = filtered.filter(q => q.category === categoryFilter);
    }

    // Apply search query if present
    if (searchQuery) {
      filtered = filtered.filter(q =>
        q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        q.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        q.tips.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (q.answer && q.answer.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (q.references && q.references.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return filtered;
  }, [selectedQuestionSet, searchQuery, categoryFilter]);

  // Filter saved questions based on search query and category
  const filteredSavedQuestions = useMemo(() => {
    if (!savedQuestions) return [];

    let filtered = savedQuestions.filter(saved => {
      if (saved.questionType === 'common') {
        return commonQuestions.find(q => q.id.toString() === saved.questionId);
      }
      return true;
    });

    // Apply category filter if not "All"
    if (categoryFilter !== 'All') {
      filtered = filtered.filter(saved => {
        if (saved.questionType === 'common') {
          const question = commonQuestions.find(q => q.id.toString() === saved.questionId);
          return question?.category === categoryFilter;
        } else {
          return saved.questionData?.category === categoryFilter;
        }
      });
    }

    // Apply search query if present
    if (searchQuery) {
      filtered = filtered.filter(saved => {
        if (saved.questionType === 'common') {
          const question = commonQuestions.find(q => q.id.toString() === saved.questionId);
          return question && (
            question.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
            question.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
            question.tips.toLowerCase().includes(searchQuery.toLowerCase())
          );
        } else {
          const data = saved.questionData;
          return data && (
            data.question?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            data.category?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            data.tips?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            data.answer?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            data.references?.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }
      });
    }

    return filtered;
  }, [savedQuestions, searchQuery, categoryFilter]);

  // Filter practice answers based on search query and category
  const filteredPracticeAnswers = useMemo(() => {
    if (!practiceAnswers) return [];

    let filtered = practiceAnswers;

    // Apply category filter if not "All"
    if (categoryFilter !== 'All') {
      filtered = filtered.filter(answer => answer.questionData?.category === categoryFilter);
    }

    // Apply search query if present
    if (searchQuery) {
      filtered = filtered.filter(answer => {
        const data = answer.questionData;
        return data && (
          data.question?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          data.category?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          data.tips?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          data.answer?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          data.references?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          answer.userAnswer?.toLowerCase().includes(searchQuery.toLowerCase())
        );
      });
    }

    return filtered;
  }, [practiceAnswers, searchQuery, categoryFilter]);

  // Get all unique categories from all questions
  const allCategories = useMemo(() => {
    const categories = new Set<string>();
    categories.add('All'); // Default option

    // Add categories from common questions
    commonQuestions.forEach(q => categories.add(q.category));

    // Add categories from generated questions
    if (questionSets) {
      questionSets.forEach(set =>
        set.questions.forEach(q => categories.add(q.category))
      );
    }

    return Array.from(categories).sort();
  }, [commonQuestions, questionSets]);

  // SECURE: Save/unsave questions using server-side operations
  const toggleSaveQuestion = async (id: number) => {
    try {
      const isCurrentlySaved = savedQuestions?.some(saved =>
        saved.questionType === 'common' && saved.questionId === id.toString()
      );

      if (isCurrentlySaved) {
        const savedQuestion = savedQuestions.find(saved =>
          saved.questionType === 'common' && saved.questionId === id.toString()
        );
        if (savedQuestion) {
          await removeSavedQuestionAction({ id: savedQuestion.id });
          refetchSavedQuestions();
          toast({
            title: "Question removed from saved",
            status: "info",
            duration: 2000,
            isClosable: true,
          });
        }
      } else {
        const question = commonQuestions.find(q => q.id === id);
        if (question) {
          await saveQuestionAction({
            questionType: 'common',
            questionId: id.toString(),
            questionData: question
          });
          refetchSavedQuestions();
          toast({
            title: "Question saved",
            status: "success",
            duration: 2000,
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.error('Error toggling saved question:', error);
      toast({
        title: "Error",
        description: "Failed to save/unsave question",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Notes are now handled through the saved questions system
  const handleNoteChange = async (id: number, note: string) => {
    try {
      const question = commonQuestions.find(q => q.id === id);
      if (question) {
        await saveQuestionAction({
          questionType: 'common',
          questionId: id.toString(),
          questionData: question,
          notes: note
        });
        refetchSavedQuestions();
      }
    } catch (error) {
      console.error('Error saving note:', error);
    }
  };



  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString() + ' ' + new Date(date).toLocaleTimeString();
  };

  // SECURE: Handle generate questions with server-side storage
  const handleGenerateQuestions = async () => {
    if (!selectedJobId) {
      toast({
        title: "No job selected",
        description: "Please select a job to generate interview questions",
        status: "warning",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsGenerating(true);
    try {
      const result = await generateQuestions({ jobId: selectedJobId });

      // Refresh the question sets from server
      refetchQuestionSets();

      // Set the newly generated set as selected
      if (result.id) {
        setSelectedQuestionSet(result);
      }

      toast({
        title: "Questions generated",
        description: `Generated interview questions for ${result.jobTitle} at ${result.company}`,
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      onClose();
    } catch (error: any) {
      console.error('Error generating questions:', error);

      // Check if it's a payment error
      if (error?.statusCode === 402 || error?.message?.includes('credits') || error?.message?.includes('paid')) {
        upgradeOnOpen();
      } else {
        toast({
          title: "Error",
          description: "Failed to generate interview questions. Please try again.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // SECURE: Toggle save for generated questions using server-side operations
  const toggleSaveGeneratedQuestion = async (question: GeneratedQuestion) => {
    try {
      const isCurrentlySaved = savedQuestions?.some(saved =>
        saved.questionType === 'generated' &&
        saved.questionData?.question === question.question
      );

      if (isCurrentlySaved) {
        const savedQuestion = savedQuestions.find(saved =>
          saved.questionType === 'generated' &&
          saved.questionData?.question === question.question
        );
        if (savedQuestion) {
          await removeSavedQuestionAction({ id: savedQuestion.id });
          refetchSavedQuestions();
          toast({
            title: "Question removed from saved",
            status: "info",
            duration: 2000,
            isClosable: true,
          });
        }
      } else {
        await saveQuestionAction({
          questionType: 'generated',
          questionData: question
        });
        refetchSavedQuestions();
        toast({
          title: "Question saved",
          status: "success",
          duration: 2000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error toggling saved generated question:', error);
      toast({
        title: "Error",
        description: "Failed to save/unsave question",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // SECURE: Handle note change for generated questions using server-side operations
  const handleGeneratedNoteChange = async (questionText: string, note: string) => {
    try {
      const question = selectedQuestionSet?.questions.find(q => q.question === questionText);
      if (question) {
        await saveQuestionAction({
          questionType: 'generated',
          questionData: question,
          notes: note
        });
        refetchSavedQuestions();
      }
    } catch (error) {
      console.error('Error saving generated note:', error);
    }
  };



  // SECURE: Save practice item using server-side operations
  const savePracticeItem = async () => {
    try {
      if (selectedQuestion) {
        const question = commonQuestions.find(q => q.id === selectedQuestion);
        if (question && practiceAnswer.trim()) {
          await savePracticeAnswerAction({
            questionType: 'common',
            questionData: question,
            userAnswer: practiceAnswer
          });

          refetchPracticeAnswers();
          setSelectedQuestion(null);
          setPracticeAnswer('');

          toast({
            title: "Practice answer saved",
            status: "success",
            duration: 2000,
            isClosable: true,
          });
        }
      } else if (selectedGeneratedQuestion && generatedPracticeAnswer.trim()) {
        await savePracticeAnswerAction({
          questionType: 'generated',
          questionData: selectedGeneratedQuestion,
          userAnswer: generatedPracticeAnswer
        });

        refetchPracticeAnswers();
        setSelectedGeneratedQuestion(null);
        setGeneratedPracticeAnswer('');

        toast({
          title: "Practice answer saved",
          status: "success",
          duration: 2000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error saving practice answer:', error);
      toast({
        title: "Error",
        description: "Failed to save practice answer",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // SECURE: Delete practice item using server-side operations
  const deletePracticeItem = async (id: string) => {
    try {
      await deletePracticeAnswerAction({ id });
      refetchPracticeAnswers();

      toast({
        title: "Practice item deleted",
        status: "info",
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error deleting practice item:', error);
      toast({
        title: "Error",
        description: "Failed to delete practice item",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Helper functions for practice question selection
  const selectQuestionForPractice = (questionId: number) => {
    setSelectedQuestion(questionId);
    setSelectedGeneratedQuestion(null);
    setPracticeAnswer('');
  };

  const selectGeneratedQuestionForPractice = (question: GeneratedQuestion) => {
    setSelectedGeneratedQuestion(question);
    setSelectedQuestion(null);
    setGeneratedPracticeAnswer('');
  };

  // Handle job selection for generation
  const handleJobSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedJobId(e.target.value);
  };



  return (
    <ContentPageBox>
      <PageHeader
        title="Interview Preparation"
        subtitle="Practice common interview questions and prepare your responses"
      >
        {user && (
          <CreditsIndicator
            credits={user.credits}
            hasPaid={user.hasPaid}
            isUsingLn={user.isUsingLn}
            size="sm"
          />
        )}
        <ActionButton
          icon={FaRobot}
          label="Generate Questions"
          variant="primary"
          onClick={onOpen}
        />
        <ActionButton
          icon={FaSearch}
          label="Search Questions"
          variant="outline"
          onClick={() => document.getElementById('search-input')?.focus()}
        />
      </PageHeader>

      <ContentContainer delay={0.3}>
        <HStack spacing={4} mb={4}>
          <Input
            id="search-input"
            placeholder="Search questions by keyword..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            size="md"
            borderRadius="md"
            _focus={{ borderColor: 'purple.400', boxShadow: '0 0 0 1px purple.400' }}
            flex="1"
          />
          <Select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            size="md"
            width="200px"
            borderRadius="md"
            _focus={{ borderColor: 'purple.400', boxShadow: '0 0 0 1px purple.400' }}
          >
            {allCategories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </Select>
          {(searchQuery || categoryFilter !== 'All') && (
            <Button
              size="md"
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setCategoryFilter('All');
              }}
              leftIcon={<Icon as={FaTimes} />}
            >
              Clear
            </Button>
          )}
        </HStack>

        <Tabs colorScheme="purple" variant="enclosed" isLazy defaultIndex={0}>
          <TabList>
            <Tab>Generated Questions ({questionSets ? questionSets.reduce((total, set) => total + set.questions.length, 0) : 0})</Tab>
            <Tab>Common Questions</Tab>
            <Tab>Saved Questions ({savedQuestions?.length || 0})</Tab>
            <Tab>Practice ({practiceAnswers?.length || 0})</Tab>
          </TabList>

          <TabPanels>
            {/* Generated Questions Tab - First and Default */}
            <TabPanel p={0} pt={4}>
              {questionSets && questionSets.length > 0 ? (
                <Grid templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }} gap={6}>
                  {/* Left side - List of question sets */}
                  <GridItem>
                    <ContentContainer delay={0.3} maxH="calc(100vh - 250px)" overflowY="auto">
                      <Heading size="sm" mb={3}>Question Sets</Heading>
                      <VStack spacing={2} align="stretch">
                        {questionSets.map((set) => (
                          <Box
                            key={set.id}
                            p={3}
                            borderRadius="md"
                            borderLeft={set.id === selectedQuestionSet?.id ? '2px solid' : 'none'}
                            borderColor={set.id === selectedQuestionSet?.id ? 'purple.400' : 'transparent'}
                            bg={set.id === selectedQuestionSet?.id ? 'gray.50' : 'transparent'}
                            cursor="pointer"
                            onClick={() => setSelectedQuestionSet(set)}
                            _hover={{ bg: 'gray.50' }}
                            transition="all 0.15s"
                            boxShadow="none"
                            mb={1}
                          >
                            <VStack align="stretch" spacing={2}>
                              <HStack justify="space-between" align="flex-start">
                                <Heading size="sm" fontWeight="medium">{set.jobTitle}</Heading>
                                <HStack>
                                  <Badge colorScheme="green" fontSize="xs" variant="subtle">
                                    {set.questions.length} Questions
                                  </Badge>
                                  <Box
                                    as="button"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // TODO: Implement server-side delete for question sets
                                      // For now, just show a message
                                      toast({
                                        title: "Feature coming soon",
                                        description: "Question set deletion will be available soon",
                                        status: "info",
                                        duration: 2000,
                                        isClosable: true,
                                      });
                                    }}
                                    color="red.400"
                                    fontSize="xs"
                                    opacity="0.7"
                                    _hover={{ opacity: 1 }}
                                  >
                                    <Icon as={FaTrash} boxSize={3} />
                                  </Box>
                                </HStack>
                              </HStack>
                              <Text fontSize="xs" color="gray.500">{set.company}</Text>
                              <Text fontSize="xs" color="gray.500">
                                Generated {formatDate(new Date(set.generatedAt))}
                              </Text>
                            </VStack>
                          </Box>
                        ))}
                      </VStack>
                    </ContentContainer>
                  </GridItem>

                  {/* Right side - Question set preview */}
                  <GridItem>
                    {selectedQuestionSet ? (
                      <ContentContainer
                        height="calc(100vh - 250px)"
                        overflowY="auto"
                        delay={0.4}
                      >
                        <VStack align="stretch" spacing={4}>
                          <Box>
                            <HStack justify="space-between" mb={2}>
                              <Heading size="sm" fontWeight="medium">{selectedQuestionSet.jobTitle}</Heading>
                              <Badge colorScheme="green">{selectedQuestionSet.questions.length} Questions</Badge>
                            </HStack>
                            <Text fontSize="sm" color="gray.500" mb={4}>{selectedQuestionSet.company}</Text>

                            <Accordion allowMultiple>
                              {selectedQuestionSet.questions.map((q, index) => (
                                <AccordionItem key={index} mb={2} border="1px solid" borderColor={borderColor} borderRadius="md">
                                  <h2>
                                    <AccordionButton py={2}>
                                      <Box flex="1" textAlign="left">
                                        <Text fontWeight="medium" fontSize="sm">{q.question}</Text>
                                      </Box>
                                      <Tag size="sm" colorScheme="green" mr={2}>{q.category}</Tag>
                                      <Box
                                        as="button"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          toggleSaveGeneratedQuestion(q);
                                        }}
                                        color={savedQuestions?.some(saved =>
                                          saved.questionType === 'generated' &&
                                          saved.questionData?.question === q.question
                                        ) ? accentColor : 'gray.400'}
                                        mr={2}
                                      >
                                        {savedQuestions?.some(saved =>
                                          saved.questionType === 'generated' &&
                                          saved.questionData?.question === q.question
                                        ) ? <FaBookmark /> : <FaRegLightbulb />}
                                      </Box>
                                      <AccordionIcon />
                                    </AccordionButton>
                                  </h2>
                                  <AccordionPanel pb={3}>
                                    <VStack align="start" spacing={4} width="100%">
                                      <Box width="100%">
                                        <Text fontStyle="italic" color="gray.600" _dark={{ color: 'gray.300' }} fontSize="sm" mb={2}>
                                          <Box as="span" fontWeight="bold">Tip:</Box> {q.tips}
                                        </Text>
                                      </Box>

                                      {q.answer && (
                                        <Box width="100%">
                                          <Text fontWeight="bold" fontSize="sm" color="green.600" mb={1}>Sample Answer:</Text>
                                          <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap" pl={2} borderLeft="2px solid" borderColor="green.200">
                                            {q.answer}
                                          </Text>
                                        </Box>
                                      )}

                                      {q.references && (
                                        <Box width="100%" mt={1}>
                                          <Text fontWeight="bold" fontSize="xs" color="blue.600" mb={1}>References:</Text>
                                          <Text fontSize="xs" color="gray.600" whiteSpace="pre-wrap" pl={2} borderLeft="2px solid" borderColor="blue.200">
                                            {q.references}
                                          </Text>
                                        </Box>
                                      )}

                                      <HStack spacing={2}>
                                        <Button
                                          size="xs"
                                          colorScheme="purple"
                                          variant="outline"
                                          onClick={() => selectGeneratedQuestionForPractice(q)}
                                          leftIcon={<FaLightbulb />}
                                        >
                                          Practice This Question
                                        </Button>
                                      </HStack>
                                    </VStack>
                                  </AccordionPanel>
                                </AccordionItem>
                              ))}
                            </Accordion>
                          </Box>
                        </VStack>
                      </ContentContainer>
                    ) : (
                      <ContentContainer
                        height="calc(100vh - 250px)"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        delay={0.4}
                      >
                        {questionSets && questionSets.length > 0 ? (
                          <Text fontSize="sm" color="gray.500">
                            Select a question set from the list to preview its questions
                          </Text>
                        ) : (
                          <EmptyInterviewQuestions
                            size="sm"
                            primaryAction={{
                              label: 'Generate Questions',
                              onClick: onOpen,
                              icon: <FaRobot />,
                              colorScheme: 'purple',
                            }}
                          />
                        )}
                      </ContentContainer>
                    )}
                  </GridItem>
                </Grid>
              ) : (
                <VStack spacing={6} py={10} align="center">
                  <Text color="gray.500">
                    You haven't generated any interview questions yet.
                  </Text>
                  <Button
                    colorScheme="purple"
                    leftIcon={<FaRobot />}
                    onClick={onOpen}
                  >
                    Generate Questions
                  </Button>
                </VStack>
              )}
            </TabPanel>

            {/* Common Questions Panel */}
            <TabPanel p={0} pt={4}>
              <Accordion allowMultiple>
                {filteredCommonQuestions.map(q => (
                  <AccordionItem key={q.id} mb={2} border="1px solid" borderColor={borderColor} borderRadius="md">
                    <h2>
                      <AccordionButton py={3}>
                        <Box flex="1" textAlign="left">
                          <HStack>
                            <Text fontWeight="medium">{q.question}</Text>
                            <Tag size="sm" colorScheme="purple" ml={2}>{q.category}</Tag>
                          </HStack>
                        </Box>
                        <HStack spacing={2}>
                          <Box
                            as="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleSaveQuestion(q.id);
                            }}
                            color={savedQuestions?.some(saved =>
                              saved.questionType === 'common' &&
                              saved.questionId === q.id.toString()
                            ) ? accentColor : 'gray.400'}
                          >
                            {savedQuestions?.some(saved =>
                              saved.questionType === 'common' &&
                              saved.questionId === q.id.toString()
                            ) ? <FaBookmark /> : <FaRegLightbulb />}
                          </Box>
                          <AccordionIcon />
                        </HStack>
                      </AccordionButton>
                    </h2>
                    <AccordionPanel pb={4}>
                      <VStack align="start" spacing={3}>
                        <Text fontStyle="italic" color="gray.600" _dark={{ color: 'gray.300' }}>
                          <Box as="span" fontWeight="bold">Tip:</Box> {q.tips}
                        </Text>
                        <Divider />
                        <Heading size="xs" mb={1}>Your Notes:</Heading>
                        <Textarea
                          placeholder="Add your notes here..."
                          value={savedQuestions?.find(saved =>
                            saved.questionType === 'common' &&
                            saved.questionId === q.id.toString()
                          )?.notes || ''}
                          onChange={(e) => {
                            // Use the server-side note handling function
                            const question = commonQuestions.find(question => question.id === q.id);
                            if (question) {
                              saveQuestionAction({
                                questionType: 'common',
                                questionId: q.id.toString(),
                                questionData: question,
                                notes: e.target.value
                              }).then(() => refetchSavedQuestions());
                            }
                          }}
                          size="sm"
                          rows={3}
                        />
                        <Button
                          size="sm"
                          colorScheme="purple"
                          variant="outline"
                          onClick={() => selectQuestionForPractice(q.id)}
                          leftIcon={<FaLightbulb />}
                        >
                          Practice This Question
                        </Button>
                      </VStack>
                    </AccordionPanel>
                  </AccordionItem>
                ))}
              </Accordion>
              {filteredCommonQuestions.length === 0 && (
                <Text textAlign="center" py={4} color="gray.500">
                  No questions match your search criteria. Try adjusting your search terms.
                </Text>
              )}
            </TabPanel>

            {/* Saved Questions Panel - Combined regular and generated */}
            <TabPanel p={0} pt={4}>
              {savedQuestions && savedQuestions.length > 0 ? (
                <>
                  {filteredSavedQuestions.length > 0 ? (
                    <Accordion allowMultiple>
                  {/* Saved questions */}
                  {filteredSavedQuestions.map((saved, index) => {
                    const questionData = saved.questionType === 'common'
                      ? commonQuestions.find(q => q.id.toString() === saved.questionId)
                      : saved.questionData;

                    if (!questionData) return null;

                    return (
                      <AccordionItem key={`saved-${saved.id}`} mb={2} border="1px solid" borderColor={borderColor} borderRadius="md">
                        <h2>
                          <AccordionButton py={3}>
                            <Box flex="1" textAlign="left">
                              <HStack>
                                <Text fontWeight="medium">{questionData.question}</Text>
                                <Tag size="sm" colorScheme={saved.questionType === 'common' ? 'purple' : 'green'} ml={2}>
                                  {questionData.category}
                                </Tag>
                              </HStack>
                            </Box>
                            <HStack spacing={2}>
                              <Box
                                as="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (saved.questionType === 'common') {
                                    toggleSaveQuestion(parseInt(saved.questionId || '0'));
                                  } else {
                                    toggleSaveGeneratedQuestion(questionData);
                                  }
                                }}
                                color={accentColor}
                              >
                                <FaBookmark />
                              </Box>
                              <AccordionIcon />
                            </HStack>
                          </AccordionButton>
                        </h2>
                        <AccordionPanel pb={4}>
                          <VStack align="start" spacing={3} width="100%">
                            <Text fontStyle="italic" color="gray.600" _dark={{ color: 'gray.300' }}>
                              <Box as="span" fontWeight="bold">Tip:</Box> {questionData.tips}
                            </Text>

                            {/* Show AI answer for generated questions */}
                            {saved.questionType === 'generated' && questionData.answer && (
                              <Box width="100%">
                                <Accordion allowToggle width="100%">
                                  <AccordionItem border="none">
                                    <h3>
                                      <AccordionButton
                                        px={0}
                                        _hover={{ bg: 'transparent' }}
                                        _focus={{ boxShadow: 'none' }}
                                      >
                                        <Box as="span" flex='1' textAlign='left' fontWeight="bold" fontSize="sm" color="green.600">
                                          Sample Answer
                                        </Box>
                                        <AccordionIcon color="green.600" />
                                      </AccordionButton>
                                    </h3>
                                    <AccordionPanel pb={2} pt={0}>
                                      <VStack align="start" spacing={2} width="100%">
                                        <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
                                          {questionData.answer}
                                        </Text>

                                        {questionData.references && (
                                          <Box width="100%" mt={2} pt={2} borderTopWidth="1px" borderColor="gray.200">
                                            <Text fontSize="xs" fontWeight="bold" color="gray.600">References:</Text>
                                            <Text fontSize="xs" color="gray.600" whiteSpace="pre-wrap">
                                              {questionData.references}
                                            </Text>
                                          </Box>
                                        )}
                                      </VStack>
                                    </AccordionPanel>
                                  </AccordionItem>
                                </Accordion>
                              </Box>
                            )}

                            <Divider />
                            <Heading size="xs" mb={1}>Your Notes:</Heading>
                            <Textarea
                              placeholder="Add your notes here..."
                              value={saved.notes || ''}
                              onChange={(e) => {
                                if (saved.questionType === 'common') {
                                  const question = commonQuestions.find(q => q.id.toString() === saved.questionId);
                                  if (question) {
                                    saveQuestionAction({
                                      questionType: 'common',
                                      questionId: saved.questionId || '0',
                                      questionData: question,
                                      notes: e.target.value
                                    }).then(() => refetchSavedQuestions());
                                  }
                                } else {
                                  saveQuestionAction({
                                    questionType: 'generated',
                                    questionData: questionData,
                                    notes: e.target.value
                                  }).then(() => refetchSavedQuestions());
                                }
                              }}
                              size="sm"
                              rows={3}
                            />
                            <Button
                              size="sm"
                              colorScheme="purple"
                              variant="outline"
                              onClick={() => {
                                if (saved.questionType === 'common') {
                                  selectQuestionForPractice(parseInt(saved.questionId || '0'));
                                } else {
                                  selectGeneratedQuestionForPractice(questionData);
                                }
                              }}
                              leftIcon={<FaLightbulb />}
                            >
                              Practice This Question
                            </Button>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>
                    );
                  })}


                    </Accordion>
                  ) : (
                    <Text textAlign="center" py={4} color="gray.500">
                      No saved questions match your search criteria. Try adjusting your search terms.
                    </Text>
                  )}
                </>
              ) : (
                <Text textAlign="center" py={4} color="gray.500">
                  You haven't saved any questions yet. Save questions to practice them later.
                </Text>
              )}
            </TabPanel>

            {/* Practice Panel */}
            <TabPanel p={0} pt={4}>
              <Tabs variant="soft-rounded" colorScheme="purple" size="sm" mb={4}>
                <TabList>
                  <Tab>Practice New Question</Tab>
                  <Tab>Saved Answers ({practiceAnswers?.length || 0})</Tab>
                </TabList>
                <TabPanels>
                  {/* Practice New Question Panel */}
                  <TabPanel p={0} pt={4}>
                    {selectedQuestion || selectedGeneratedQuestion ? (
                      <VStack align="stretch" spacing={4}>
                        <Box p={4} borderRadius="md" bg={bgColor} boxShadow="sm" borderWidth="1px" borderColor={borderColor}>
                          {selectedQuestion && (
                            <>
                              <Heading size="md" mb={2}>
                                {commonQuestions.find(q => q.id === selectedQuestion)?.question}
                              </Heading>
                              <Tag size="sm" colorScheme="purple" mb={3}>
                                {commonQuestions.find(q => q.id === selectedQuestion)?.category}
                              </Tag>
                              <Text fontStyle="italic" color="gray.600" _dark={{ color: 'gray.300' }} mb={4}>
                                <Box as="span" fontWeight="bold">Tip:</Box> {commonQuestions.find(q => q.id === selectedQuestion)?.tips}
                              </Text>
                              <Textarea
                                placeholder="Type your answer here..."
                                value={practiceAnswer}
                                onChange={(e) => setPracticeAnswer(e.target.value)}
                                minH="200px"
                                mb={3}
                              />
                            </>
                          )}

                          {selectedGeneratedQuestion && (
                            <>
                              <Heading size="md" mb={2}>
                                {selectedGeneratedQuestion.question}
                              </Heading>
                              <Tag size="sm" colorScheme="green" mb={3}>
                                {selectedGeneratedQuestion.category}
                              </Tag>
                              <Text fontStyle="italic" color="gray.600" _dark={{ color: 'gray.300' }} mb={2}>
                                <Box as="span" fontWeight="bold">Tip:</Box> {selectedGeneratedQuestion.tips}
                              </Text>

                              {selectedGeneratedQuestion.answer && (
                                <Box width="100%" mb={4}>
                                  <Accordion allowToggle width="100%">
                                    <AccordionItem border="1px solid" borderColor="gray.200" borderRadius="md">
                                      <h3>
                                        <AccordionButton>
                                          <Box as="span" flex='1' textAlign='left' fontWeight="bold" fontSize="sm">
                                            View Sample Answer
                                          </Box>
                                          <AccordionIcon />
                                        </AccordionButton>
                                      </h3>
                                      <AccordionPanel pb={4}>
                                        <VStack align="start" spacing={2} width="100%">
                                          <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
                                            {selectedGeneratedQuestion.answer}
                                          </Text>

                                          {selectedGeneratedQuestion.references && (
                                            <Box width="100%" mt={2} pt={2} borderTopWidth="1px" borderColor="gray.200">
                                              <Text fontSize="xs" fontWeight="bold" color="gray.600">References:</Text>
                                              <Text fontSize="xs" color="gray.600" whiteSpace="pre-wrap">
                                                {selectedGeneratedQuestion.references}
                                              </Text>
                                            </Box>
                                          )}
                                        </VStack>
                                      </AccordionPanel>
                                    </AccordionItem>
                                  </Accordion>
                                </Box>
                              )}

                              <Textarea
                                placeholder="Type your answer here..."
                                value={generatedPracticeAnswer}
                                onChange={(e) => setGeneratedPracticeAnswer(e.target.value)}
                                minH="200px"
                                mb={3}
                              />
                            </>
                          )}

                          <HStack justifyContent="flex-end">
                            <Button
                              colorScheme="purple"
                              leftIcon={<FaCheck />}
                              isDisabled={selectedQuestion ? !practiceAnswer.trim() : !generatedPracticeAnswer.trim()}
                              onClick={savePracticeItem}
                            >
                              Save Answer
                            </Button>
                          </HStack>
                        </Box>
                      </VStack>
                    ) : (
                      <VStack py={10} spacing={4}>
                        <Text textAlign="center" color="gray.500">
                          Select a question to practice from any of the question tabs.
                        </Text>
                        <HStack spacing={4}>
                          <Button
                            colorScheme="purple"
                            variant="outline"
                            onClick={() => setSelectedQuestion(commonQuestions[0].id)}
                          >
                            Practice a Random Question
                          </Button>

                          {questionSets && questionSets.length > 0 && selectedQuestionSet && (
                            <Button
                              colorScheme="green"
                              variant="outline"
                              onClick={() => selectGeneratedQuestionForPractice(selectedQuestionSet.questions[Math.floor(Math.random() * selectedQuestionSet.questions.length)])}
                            >
                              Practice a Random Generated Question
                            </Button>
                          )}
                        </HStack>
                      </VStack>
                    )}
                  </TabPanel>

                  {/* Saved Practice Answers Panel */}
                  <TabPanel p={0} pt={4}>
                    {practiceAnswers && practiceAnswers.length > 0 ? (
                      <>
                        {filteredPracticeAnswers.length > 0 ? (
                          <Accordion allowMultiple>
                            {filteredPracticeAnswers.map((answer) => {
                              const questionData = answer.questionData;
                              return (
                          <AccordionItem key={answer.id} mb={4} border="1px solid" borderColor={borderColor} borderRadius="md">
                            <h2>
                              <AccordionButton py={3}>
                                <Box flex="1" textAlign="left">
                                  <HStack>
                                    <Text fontWeight="medium">{questionData.question}</Text>
                                    <Tag
                                      size="sm"
                                      colorScheme={answer.questionType === 'common' ? 'purple' : 'green'}
                                      ml={2}
                                    >
                                      {questionData.category}
                                    </Tag>
                                  </HStack>
                                  <Text fontSize="xs" color="gray.500" mt={1}>
                                    Practiced on {new Date(answer.savedAt).toLocaleDateString()}
                                  </Text>
                                </Box>
                                <HStack spacing={2}>
                                  <Box
                                    as="button"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      deletePracticeItem(answer.id);
                                    }}
                                    color="red.400"
                                    opacity={0.7}
                                    _hover={{ opacity: 1 }}
                                  >
                                    <Icon as={FaTrash} boxSize={3} />
                                  </Box>
                                  <AccordionIcon />
                                </HStack>
                              </AccordionButton>
                            </h2>
                            <AccordionPanel pb={4}>
                              <VStack align="start" spacing={4} width="100%">
                                <Box width="100%">
                                  <Text fontStyle="italic" color="gray.600" _dark={{ color: 'gray.300' }} mb={2}>
                                    <Box as="span" fontWeight="bold">Tip:</Box> {questionData.tips}
                                  </Text>

                                  {questionData.answer && (
                                    <Box width="100%" mt={3}>
                                      <Accordion allowToggle width="100%" defaultIndex={[0]}>
                                        <AccordionItem border="1px solid" borderColor="gray.200" borderRadius="md">
                                          <h3>
                                            <AccordionButton>
                                              <Box as="span" flex='1' textAlign='left' fontWeight="bold" fontSize="sm">
                                                View Sample Answer
                                              </Box>
                                              <AccordionIcon />
                                            </AccordionButton>
                                          </h3>
                                          <AccordionPanel pb={4}>
                                            <VStack align="start" spacing={4} width="100%">
                                              <Box width="100%">
                                                <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
                                                  {questionData.answer}
                                                </Text>
                                              </Box>

                                              {questionData.references && (
                                                <Box width="100%" mt={3} pt={3} borderTopWidth="1px" borderColor="gray.200">
                                                  <Text fontSize="sm" fontWeight="bold" color="gray.700" mb={2}>References:</Text>
                                                  <Text fontSize="sm" color="gray.600" whiteSpace="pre-wrap">
                                                    {questionData.references}
                                                  </Text>
                                                </Box>
                                              )}
                                            </VStack>
                                          </AccordionPanel>
                                        </AccordionItem>
                                      </Accordion>
                                    </Box>
                                  )}
                                </Box>

                                <Divider />
                                <Heading size="xs" mb={1}>Your Practice Answer:</Heading>
                                <Box
                                  p={3}
                                  borderRadius="md"
                                  bg="gray.50"
                                  _dark={{ bg: 'gray.700', borderColor: 'gray.600' }}
                                  width="100%"
                                  borderWidth="1px"
                                  borderColor="gray.200"
                                >
                                  <Text fontSize="sm" whiteSpace="pre-wrap">
                                    {answer.userAnswer}
                                  </Text>
                                </Box>

                                {answer.notes && (
                                  <>
                                    <Heading size="xs" mb={1}>Your Notes:</Heading>
                                    <Box
                                      p={3}
                                      borderRadius="md"
                                      bg="blue.50"
                                      _dark={{ bg: 'blue.900', borderColor: 'blue.600' }}
                                      width="100%"
                                      borderWidth="1px"
                                      borderColor="blue.200"
                                    >
                                      <Text fontSize="sm" whiteSpace="pre-wrap">
                                        {answer.notes}
                                      </Text>
                                    </Box>
                                  </>
                                )}

                                <Button
                                  size="sm"
                                  colorScheme="purple"
                                  variant="outline"
                                  onClick={() => {
                                    if (answer.questionType === 'common') {
                                      const questionId = commonQuestions.find(q => q.question === questionData.question)?.id;
                                      if (questionId) selectQuestionForPractice(questionId);
                                    } else {
                                      selectGeneratedQuestionForPractice(questionData);
                                    }
                                  }}
                                  leftIcon={<FaLightbulb />}
                                  mt={2}
                                >
                                  Practice Again
                                </Button>
                              </VStack>
                            </AccordionPanel>
                          </AccordionItem>
                              );
                            })}
                          </Accordion>
                        ) : (
                          <Text textAlign="center" py={4} color="gray.500">
                            No practice items match your search criteria. Try adjusting your search terms.
                          </Text>
                        )}
                      </>
                    ) : (
                      <EmptyInterviewQuestions
                        title="No Practice Questions"
                        description="You haven't saved any questions yet. Click 'Save to Practice' on any question to add it here."
                        size="sm"
                        showActions={false}
                      />
                    )}
                  </TabPanel>
                </TabPanels>
              </Tabs>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </ContentContainer>

      {/* Generate Questions Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Generate Interview Questions</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <Text>
                Select a job to generate tailored interview questions based on the job description.
                The AI will analyze the job requirements and create relevant technical, behavioral,
                and situational questions to help you prepare.
              </Text>

              {isLoadingJobs ? (
                <SimpleLoading message="Loading your jobs..." />
              ) : jobs && jobs.length > 0 ? (
                <FormControl isRequired>
                  <FormLabel>Select Job</FormLabel>
                  <Select
                    placeholder="Choose a job"
                    value={selectedJobId}
                    onChange={handleJobSelect}
                  >
                    {jobs.map((job: any) => (
                      <option key={job.id} value={job.id}>
                        {job.title} at {job.company}
                      </option>
                    ))}
                  </Select>
                </FormControl>
              ) : (
                <EmptyJobs
                  size="sm"
                  title="No Jobs Available"
                  description="You need to add some jobs first before generating interview questions."
                  showActions={false}
                />
              )}

              {selectedJobId && (
                <Box p={4} borderWidth="1px" borderRadius="md" borderColor={borderColor} bg={bgColor}>
                  <Text fontSize="sm" mb={2}>
                    The AI will generate approximately 10 interview questions specific to this job.
                    Each question will include a tip on how to answer it effectively.
                  </Text>
                  <Text fontSize="xs" color="gray.500" fontStyle="italic">
                    ⚠️ AI-generated questions and answers are for practice only. Actual interview questions may vary.
                    Please customize responses based on your experience.
                  </Text>
                </Box>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="purple"
              onClick={handleGenerateQuestions}
              isLoading={isGenerating}
              loadingText="Generating..."
              isDisabled={!selectedJobId || isGenerating}
              leftIcon={<FaRobot />}
            >
              Generate Questions
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Upgrade Modal */}
      <UpgradeModal
        isOpen={upgradeIsOpen}
        onClose={upgradeOnClose}
        userCredits={user?.credits || 0}
        feature="interview"
      />
    </ContentPageBox>
  );
}
