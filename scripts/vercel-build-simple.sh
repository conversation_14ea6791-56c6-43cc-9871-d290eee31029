#!/bin/bash

echo "==================================================================="
echo "SIMPLE VERCEL BUILD FOR CAREERDART"
echo "==================================================================="

set -e

echo "Node version: $(node --version)"
echo "NPM version: $(npm --version)"

# Install Wasp
echo "Installing Wasp..."
curl -sSL https://get.wasp-lang.dev/installer.sh | sh

# Add to PATH
export PATH="$HOME/.local/bin:$PATH"

# Verify Wasp installation
if command -v wasp &> /dev/null; then
    echo "Wasp installed successfully: $(wasp version)"
else
    echo "Wasp not found in PATH, trying alternative locations..."
    export PATH="$HOME/.wasp/bin:$PATH"
    if command -v wasp &> /dev/null; then
        echo "Wasp found: $(wasp version)"
    else
        echo "ERROR: Wasp installation failed"
        exit 1
    fi
fi

# Install dependencies
echo "Installing dependencies..."
npm install

# Build with Wasp
echo "Building with Wasp..."
wasp build

echo "Build completed successfully!"
echo "Output directory: .wasp/build/web-app/build"
