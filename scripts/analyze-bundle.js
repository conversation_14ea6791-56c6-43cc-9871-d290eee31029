#!/usr/bin/env node

/**
 * Bundle Analysis Script
 * Analyzes the built application bundle for size, dependencies, and optimization opportunities
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeDirectory(dirPath, extensions = ['.js', '.css', '.html']) {
  const results = {
    files: [],
    totalSize: 0,
    count: 0,
  };

  if (!fs.existsSync(dirPath)) {
    return results;
  }

  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        const size = stat.size;
        results.files.push({
          path: path.relative(dirPath, fullPath),
          size,
          sizeFormatted: formatBytes(size),
        });
        results.totalSize += size;
        results.count++;
      }
    }
  }

  walkDir(dirPath);
  results.files.sort((a, b) => b.size - a.size);
  return results;
}

function analyzeDependencies() {
  log('\n📦 Analyzing Dependencies...', 'cyan');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = packageJson.dependencies || {};
    const devDependencies = packageJson.devDependencies || {};
    
    log(`Production Dependencies: ${Object.keys(dependencies).length}`, 'green');
    log(`Development Dependencies: ${Object.keys(devDependencies).length}`, 'yellow');
    
    // Check for large dependencies
    const largeDeps = [
      '@chakra-ui/react',
      'react',
      'react-dom',
      'framer-motion',
      'react-router-dom',
    ];
    
    log('\n📊 Key Dependencies:', 'bright');
    largeDeps.forEach(dep => {
      if (dependencies[dep]) {
        log(`  ${dep}: ${dependencies[dep]}`, 'blue');
      }
    });
    
    // Check for potential optimizations
    log('\n💡 Optimization Opportunities:', 'yellow');
    
    if (dependencies['lodash']) {
      log('  ⚠️  Consider using lodash-es or individual lodash functions', 'yellow');
    }
    
    if (dependencies['moment']) {
      log('  ⚠️  Consider replacing moment with date-fns or dayjs for smaller bundle', 'yellow');
    }
    
    if (dependencies['@chakra-ui/react'] && !dependencies['@chakra-ui/icons']) {
      log('  ✅ Good: Using separate icon package', 'green');
    }
    
  } catch (error) {
    log(`Error analyzing dependencies: ${error.message}`, 'red');
  }
}

function analyzeBundle() {
  log('🔍 Bundle Analysis Report', 'bright');
  log('========================\n', 'bright');
  
  // Check if build directory exists
  const buildPaths = [
    '.wasp/build/web-app/build',
    'dist',
    'build',
  ];
  
  let buildPath = null;
  for (const path of buildPaths) {
    if (fs.existsSync(path)) {
      buildPath = path;
      break;
    }
  }
  
  if (!buildPath) {
    log('❌ No build directory found. Please run the build command first.', 'red');
    log('   Try: wasp build', 'yellow');
    return;
  }
  
  log(`📁 Analyzing build directory: ${buildPath}`, 'blue');
  
  // Analyze JavaScript files
  const jsAnalysis = analyzeDirectory(buildPath, ['.js']);
  log(`\n📄 JavaScript Files (${jsAnalysis.count} files):`, 'cyan');
  log(`   Total Size: ${formatBytes(jsAnalysis.totalSize)}`, 'bright');
  
  if (jsAnalysis.files.length > 0) {
    log('\n   Largest JS Files:', 'yellow');
    jsAnalysis.files.slice(0, 10).forEach((file, index) => {
      const indicator = file.size > 500000 ? '🔴' : file.size > 100000 ? '🟡' : '🟢';
      log(`   ${index + 1}. ${indicator} ${file.path} (${file.sizeFormatted})`, 'blue');
    });
  }
  
  // Analyze CSS files
  const cssAnalysis = analyzeDirectory(buildPath, ['.css']);
  log(`\n🎨 CSS Files (${cssAnalysis.count} files):`, 'cyan');
  log(`   Total Size: ${formatBytes(cssAnalysis.totalSize)}`, 'bright');
  
  if (cssAnalysis.files.length > 0) {
    log('\n   Largest CSS Files:', 'yellow');
    cssAnalysis.files.slice(0, 5).forEach((file, index) => {
      log(`   ${index + 1}. ${file.path} (${file.sizeFormatted})`, 'blue');
    });
  }
  
  // Overall analysis
  const totalSize = jsAnalysis.totalSize + cssAnalysis.totalSize;
  log(`\n📊 Bundle Summary:`, 'bright');
  log(`   Total Bundle Size: ${formatBytes(totalSize)}`, 'cyan');
  log(`   JavaScript: ${formatBytes(jsAnalysis.totalSize)} (${((jsAnalysis.totalSize / totalSize) * 100).toFixed(1)}%)`, 'blue');
  log(`   CSS: ${formatBytes(cssAnalysis.totalSize)} (${((cssAnalysis.totalSize / totalSize) * 100).toFixed(1)}%)`, 'blue');
  
  // Performance recommendations
  log(`\n🚀 Performance Recommendations:`, 'green');
  
  if (totalSize > 5000000) { // 5MB
    log('   ⚠️  Bundle size is quite large (>5MB)', 'red');
    log('   💡 Consider code splitting and lazy loading', 'yellow');
  } else if (totalSize > 2000000) { // 2MB
    log('   ⚠️  Bundle size is moderate (>2MB)', 'yellow');
    log('   💡 Consider optimizing large dependencies', 'yellow');
  } else {
    log('   ✅ Bundle size looks good (<2MB)', 'green');
  }
  
  if (jsAnalysis.files.some(f => f.size > 1000000)) { // 1MB
    log('   ⚠️  Some JS files are very large (>1MB)', 'yellow');
    log('   💡 Consider splitting large chunks', 'yellow');
  }
  
  // Check for source maps
  const sourceMaps = analyzeDirectory(buildPath, ['.map']);
  if (sourceMaps.count > 0) {
    log(`   📍 Source maps found: ${sourceMaps.count} files (${formatBytes(sourceMaps.totalSize)})`, 'blue');
    log('   💡 Consider removing source maps in production', 'yellow');
  }
  
  analyzeDependencies();
  
  log('\n✨ Analysis Complete!', 'green');
  log('\n💡 Tips for optimization:', 'cyan');
  log('   • Use dynamic imports for code splitting', 'blue');
  log('   • Implement lazy loading for routes', 'blue');
  log('   • Optimize images and use modern formats', 'blue');
  log('   • Enable gzip/brotli compression', 'blue');
  log('   • Use tree shaking to eliminate dead code', 'blue');
  log('   • Consider using a CDN for static assets', 'blue');
}

// Check if we should run webpack-bundle-analyzer
function runWebpackAnalyzer() {
  try {
    log('\n🔬 Running Webpack Bundle Analyzer...', 'cyan');
    
    // Check if webpack-bundle-analyzer is installed
    execSync('npm list webpack-bundle-analyzer', { stdio: 'ignore' });
    
    // Try to find webpack stats file
    const statsFiles = [
      '.wasp/build/web-app/build/static/js/bundle-stats.json',
      'build/static/js/bundle-stats.json',
      'dist/stats.json',
    ];
    
    let statsFile = null;
    for (const file of statsFiles) {
      if (fs.existsSync(file)) {
        statsFile = file;
        break;
      }
    }
    
    if (statsFile) {
      log(`📊 Opening bundle analyzer with stats file: ${statsFile}`, 'blue');
      execSync(`npx webpack-bundle-analyzer ${statsFile}`, { stdio: 'inherit' });
    } else {
      log('📊 No webpack stats file found. Generating basic analysis...', 'yellow');
    }
    
  } catch (error) {
    log('ℹ️  Webpack Bundle Analyzer not available or stats file not found', 'blue');
    log('   Install with: npm install --save-dev webpack-bundle-analyzer', 'yellow');
  }
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    log('Bundle Analysis Script', 'bright');
    log('Usage: node scripts/analyze-bundle.js [options]', 'blue');
    log('\nOptions:', 'cyan');
    log('  --webpack, -w    Run webpack bundle analyzer', 'blue');
    log('  --help, -h       Show this help message', 'blue');
    process.exit(0);
  }
  
  analyzeBundle();
  
  if (args.includes('--webpack') || args.includes('-w')) {
    runWebpackAnalyzer();
  }
}

module.exports = {
  analyzeBundle,
  analyzeDependencies,
  analyzeDirectory,
  formatBytes,
};
