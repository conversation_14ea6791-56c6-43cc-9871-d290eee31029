#!/bin/bash

echo "==================================================================="
echo "BUILDING CAREERDART FOR VERCEL DEPLOYMENT"
echo "==================================================================="

# Exit on any error
set -e

# Debug information
echo "Current working directory: $(pwd)"
echo "Node version: $(node --version)"
echo "NPM version: $(npm --version)"
echo "Environment: ${NODE_ENV:-development}"

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    echo "Error: main.wasp not found. Please run this script from the project root."
    echo "Directory contents:"
    ls -la
    exit 1
fi

# Install Wasp if not already installed
if ! command -v wasp &> /dev/null; then
    echo "Installing Wasp..."

    # Try the official installer
    if curl -sSL https://get.wasp-lang.dev/installer.sh | sh; then
        echo "Wasp installer completed"
    else
        echo "Official installer failed, trying alternative method..."
        # Try downloading directly
        mkdir -p $HOME/.local/bin
        WASP_VERSION="0.15.0"
        WASP_URL="https://github.com/wasp-lang/wasp/releases/download/v${WASP_VERSION}/wasp-linux-x86_64.tar.gz"

        if curl -L "$WASP_URL" | tar -xz -C $HOME/.local/bin --strip-components=1; then
            echo "Direct download successful"
        else
            echo "Direct download failed, trying npm-based approach..."
            # As a last resort, try to use npx or install via npm if available
            if npm install -g @wasp-lang/wasp 2>/dev/null; then
                echo "Installed via npm"
            else
                echo "All installation methods failed"
                echo "Attempting to continue without Wasp (this may fail)..."
            fi
        fi
    fi

    # Update PATH
    export PATH="$HOME/.local/bin:$PATH"

    # Also try adding to current shell
    source ~/.bashrc 2>/dev/null || true
    source ~/.profile 2>/dev/null || true

    # Try alternative paths
    export PATH="$HOME/.wasp/bin:$PATH"
    export PATH="/usr/local/bin:$PATH"
fi

# Check if Wasp is now available
if command -v wasp &> /dev/null; then
    echo "Using Wasp version: $(wasp version)"
else
    echo "ERROR: Wasp is still not available after installation attempts"
    echo "PATH: $PATH"
    echo "Checking for wasp in common locations:"
    find $HOME -name "wasp" -type f 2>/dev/null || echo "No wasp binary found"
    echo "This deployment requires Wasp to be available. Please check the Wasp installation."
    exit 1
fi

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf .wasp/build
rm -rf api

# Install dependencies
echo "Installing dependencies..."
npm install

# Generate Prisma client
echo "Generating Prisma client..."
if ! wasp db generate; then
    echo "Error: Failed to generate Prisma client"
    exit 1
fi

# Run database migrations (only if DATABASE_URL is set)
if [ ! -z "$DATABASE_URL" ]; then
    echo "Running database migrations..."
    if ! wasp db migrate-deploy; then
        echo "Warning: Database migration failed, continuing anyway..."
    fi
else
    echo "Warning: DATABASE_URL not set, skipping migrations"
fi

# Build the application
echo "Building application for production..."
if ! wasp build; then
    echo "Error: Wasp build failed"
    exit 1
fi

# Verify build output
if [ ! -d ".wasp/build" ]; then
    echo "Error: Build failed - .wasp/build directory not found"
    exit 1
fi

# Copy static files
echo "Copying static files..."
if [ -d "public" ]; then
    mkdir -p .wasp/build/web-app/build
    cp -r public/* .wasp/build/web-app/build/ 2>/dev/null || true
fi

# Create API routes for Vercel (if server build exists)
if [ -d ".wasp/build/server" ]; then
    echo "Setting up API routes..."
    mkdir -p api
    cp -r .wasp/build/server/* api/ 2>/dev/null || true
fi

# Create necessary directories
mkdir -p .wasp/build/web-app/build/static

echo "==================================================================="
echo "BUILD COMPLETED SUCCESSFULLY!"
echo "==================================================================="
echo "Build output: .wasp/build/web-app/build/"
echo "API routes: api/"
echo "Ready for Vercel deployment!"
