#!/bin/bash

echo "==================================================================="
echo "BUILDING CAREERDART LOCALLY FOR VERCEL DEPLOYMENT"
echo "==================================================================="

# Exit on any error
set -e

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    echo "Error: main.wasp not found. Please run this script from the project root."
    exit 1
fi

# Check if Wasp is available locally
if ! command -v wasp &> /dev/null; then
    echo "Error: Wasp is not installed locally."
    echo "Please install Wasp first: curl -sSL https://get.wasp-lang.dev/installer.sh | sh"
    exit 1
fi

echo "Using Wasp version: $(wasp version)"

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf .wasp/build
rm -rf dist
rm -rf build

# Install dependencies
echo "Installing dependencies..."
npm install

# Generate Prisma client
echo "Generating Prisma client..."
wasp db generate

# Build the application
echo "Building application for production..."
wasp build

# Verify build output
if [ ! -d ".wasp/build" ]; then
    echo "Error: Build failed - .wasp/build directory not found"
    exit 1
fi

# Create a dist directory for Vercel
echo "Preparing build for Vercel..."
mkdir -p dist

# Copy the web app build
if [ -d ".wasp/build/web-app/build" ]; then
    cp -r .wasp/build/web-app/build/* dist/
    echo "✅ Web app build copied to dist/"
else
    echo "❌ Web app build not found"
    exit 1
fi

# Copy static files
if [ -d "public" ]; then
    cp -r public/* dist/ 2>/dev/null || true
    echo "✅ Static files copied"
fi

# Create a simple package.json for the dist directory
cat > dist/package.json << EOF
{
  "name": "careerdart-build",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "start": "echo 'This is a static build'"
  }
}
EOF

echo "==================================================================="
echo "LOCAL BUILD COMPLETED SUCCESSFULLY!"
echo "==================================================================="
echo "Build output: dist/"
echo ""
echo "Next steps:"
echo "1. Commit the dist/ directory to your repository"
echo "2. Update vercel.json to use dist/ as output directory"
echo "3. Deploy to Vercel"
echo ""
echo "Commands to run:"
echo "  git add dist/"
echo "  git commit -m 'Add pre-built assets for Vercel'"
echo "  git push origin main"
