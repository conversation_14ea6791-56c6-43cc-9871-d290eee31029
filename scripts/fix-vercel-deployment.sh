#!/bin/bash

echo "==================================================================="
echo "FIXING VERCEL DEPLOYMENT FOR CAREERDART"
echo "==================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    print_error "main.wasp not found. Please run this script from the project root."
    exit 1
fi

print_status "Implementing the pre-build solution for Vercel deployment..."

# Step 1: Check if Wasp is available locally
if ! command -v wasp &> /dev/null; then
    print_error "Wasp is not installed locally."
    echo "Please install Wasp first:"
    echo "  curl -sSL https://get.wasp-lang.dev/installer.sh | sh"
    echo "  source ~/.bashrc"
    exit 1
fi

print_success "Wasp is available: $(wasp version)"

# Step 2: Build locally
print_status "Building application locally..."
if ./scripts/build-local-for-vercel.sh; then
    print_success "Local build completed successfully"
else
    print_error "Local build failed"
    exit 1
fi

# Step 3: Update Vercel configuration
print_status "Updating Vercel configuration for static deployment..."
cp vercel-static.json vercel.json
print_success "Vercel configuration updated"

# Step 4: Add to git (but don't commit yet)
print_status "Adding build artifacts to git..."
git add dist/
git add vercel.json

# Check if there are changes to commit
if git diff --cached --quiet; then
    print_warning "No changes to commit"
else
    print_status "Ready to commit changes"
    echo ""
    echo "Files to be committed:"
    git diff --cached --name-only
    echo ""
    
    # Ask user if they want to commit
    read -p "Do you want to commit these changes? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git commit -m "Add pre-built assets for Vercel deployment

- Built application locally using Wasp
- Added dist/ directory with static assets
- Updated vercel.json for static deployment
- This fixes the 'wasp: command not found' error in Vercel"
        
        print_success "Changes committed successfully"
        
        # Ask if they want to push
        read -p "Do you want to push to origin? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            git push origin main
            print_success "Changes pushed to origin"
            
            echo ""
            print_status "Next steps:"
            echo "1. Go to your Vercel dashboard"
            echo "2. Your project should automatically redeploy"
            echo "3. If not, run: vercel --prod"
            echo ""
            print_success "Deployment fix completed!"
        else
            print_warning "Changes committed but not pushed. Run 'git push origin main' when ready."
        fi
    else
        print_warning "Changes staged but not committed. Review and commit manually."
    fi
fi

echo ""
print_status "Summary of changes:"
echo "✅ Built application locally with Wasp"
echo "✅ Created dist/ directory with static assets"
echo "✅ Updated vercel.json for static deployment"
echo "✅ This eliminates the need for Wasp in Vercel's build environment"
echo ""
print_success "Your CareerDart app should now deploy successfully to Vercel!"
