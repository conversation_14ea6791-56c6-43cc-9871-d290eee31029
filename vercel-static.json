{"version": 2, "name": "<PERSON><PERSON><PERSON>", "buildCommand": "echo 'Using pre-built assets'", "outputDirectory": "dist", "installCommand": "echo 'No installation needed for static build'", "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/stripe-webhook", "dest": "/api/stripe-webhook"}, {"src": "/ln-login", "dest": "/api/ln-login"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"NODE_ENV": "production"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}