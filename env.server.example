# defaults to localhost:3001 during development. make sure to add the deployed server address in production.
WASP_SERVER_URL=http://localhost:3001

# create an api key at https://platform.openai.com/
OPENAI_API_KEY=

# Guide for setting up google OAuth2: https://wasp-lang.dev/docs/integrations/google
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# you can easily set up a hosted database at https://railway.app
DATABASE_URL=

# sendGrid key for sending out emails https://sendgrid.com/
SENDGRID_API_KEY=
SEND_EMAILS_IN_DEVELOPMENT=true

# Stripe Payment Keys https://stripe.com
STRIPE_KEY=
PRODUCT_PRICE_ID=
PRODUCT_CREDITS_PRICE_ID=

