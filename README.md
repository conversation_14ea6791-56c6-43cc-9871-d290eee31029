# 🎯 CareerDart - Your Personal Career Companion

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/yonasnh/careerdart)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Wasp](https://img.shields.io/badge/Wasp-FFCC02?logo=wasp&logoColor=black)](https://wasp-lang.dev/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

<div align="center">
  <img src='public/careerdart-hero.png' width='800px' alt='CareerDart - AI-Powered Career Intelligence Platform'/>
</div>

**CareerDart** is a revolutionary AI-powered career development platform that provides personalized career intelligence. Navigate your career journey with custom-built resumes, intelligent cover letters, AI-powered interview preparation, and comprehensive career growth strategies—all in one place.

## ✨ Features

### 🎯 **Personalized Career Intelligence**
- **AI-Powered Career Analysis**: Intelligent assessment of your skills, experience, and career goals
- **Custom Strategy Development**: Personalized career roadmaps tailored to your industry and aspirations
- **Market Intelligence**: Real-time insights into job market trends and opportunities

### 📄 **Smart Resume Builder**
- **Professional Templates**: Curated collection of ATS-optimized resume templates
- **AI Content Optimization**: Intelligent suggestions for improving resume content and formatting
- **Multi-Format Export**: PDF generation with print-ready and digital-optimized versions
- **Template Customization**: Full control over styling, colors, fonts, and layout

### ✉️ **Intelligent Cover Letter Generation**
- **Personalized Content**: AI-generated cover letters tailored to specific job descriptions
- **Company Research Integration**: Automatic incorporation of company-specific insights
- **Tone Customization**: Adjustable creativity and professionalism levels
- **Multiple Versions**: Generate variations for different applications

### 🎤 **AI Interview Preparation**
- **Custom Question Generation**: Job-specific interview questions based on role requirements
- **Practice Sessions**: Interactive interview simulation with AI feedback
- **Answer Optimization**: AI-powered suggestions for improving interview responses
- **Industry-Specific Prep**: Tailored preparation for different sectors and roles

### 💼 **Comprehensive Job Tracking**
- **Application Management**: Organize and track all job applications in one place
- **LinkedIn Integration**: Direct import of job descriptions from LinkedIn URLs
- **Status Monitoring**: Real-time tracking of application progress and follow-ups
- **Analytics Dashboard**: Insights into application success rates and patterns

### 📚 **Learning & Development Center**
- **Curated Resources**: Professional development content categorized by career stage
- **Video Learning**: Embedded tutorials from YouTube and TikTok
- **Progress Tracking**: Achievement badges and completion monitoring
- **Personalized Recommendations**: AI-suggested learning paths based on career goals

## 🚀 Quick Start

### Prerequisites

- **Node.js** (v18 or higher)
- **PostgreSQL** (v13 or higher)
- **OpenAI API key**
- **Stripe account** (for payments)
- **Google OAuth credentials**

### Installation

1. **Install Wasp**:
```bash
curl -sSL https://get.wasp-lang.dev/installer.sh | sh
```

2. **Clone and Setup**:
```bash
git clone https://github.com/yonasnh/careerdart.git
cd careerdart
npm install
```

3. **Environment Configuration**:
Create a `.env.server` file:
```env
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/careerdart

# AI Services
OPENAI_API_KEY=your_openai_api_key

# Payment Processing
STRIPE_KEY=your_stripe_secret_key
PRODUCT_PRICE_ID=your_stripe_price_id
GPT4_PRICE_ID=your_gpt4_price_id
PRODUCT_CREDITS_PRICE_ID=your_credits_price_id

# Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Application
WASP_WEB_CLIENT_URL=http://localhost:3000
WASP_SERVER_URL=http://localhost:3001
```

4. **Database Setup**:
```bash
# Option 1: Use Docker (recommended)
wasp start db

# Option 2: Use external database
# Set DATABASE_URL in .env.server to your PostgreSQL connection string
```

5. **Run Migrations and Start**:
```bash
wasp db migrate-dev
wasp start
```

6. **Access Application**:
- Frontend: `http://localhost:3000`
- Backend: `http://localhost:3001`

7. **Development Tools**:
Install the [Wasp VSCode extension](https://marketplace.visualstudio.com/items?itemName=wasp-lang.wasp) for the best development experience.

## 🛠️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Chakra UI** for component library
- **Framer Motion** for animations
- **React Query** for state management
- **React Router** for navigation

### Backend
- **Wasp Framework** for full-stack development
- **Node.js** runtime
- **PostgreSQL** with Prisma ORM
- **Express.js** middleware

### AI & Integrations
- **OpenAI GPT-4** for content generation
- **Stripe** for payment processing
- **Lightning Network** for Bitcoin payments
- **Google OAuth** for authentication

### DevOps & Monitoring
- **Vitest** for unit testing
- **Playwright** for E2E testing
- **Performance monitoring** with Core Web Vitals
- **Error tracking** and reporting

## 📁 Project Structure

```
careerdart/
├── 📁 src/
│   ├── 📁 client/                 # Frontend React application
│   │   ├── 📁 components/         # Reusable UI components
│   │   │   ├── ErrorBoundary.tsx  # Error handling component
│   │   │   ├── NavBar.tsx         # Navigation component
│   │   │   ├── Footer.tsx         # Footer with legal links
│   │   │   ├── UndrawIllustration.tsx # Professional illustrations
│   │   │   └── ...
│   │   ├── 📁 legal/              # Legal pages (Privacy, TOS)
│   │   │   ├── PrivacyPolicyPage.tsx
│   │   │   └── TosPage.tsx
│   │   ├── 📁 hooks/              # Custom React hooks
│   │   │   ├── useFormValidation.ts
│   │   │   └── useOptimizedAuth.ts
│   │   ├── 📁 utils/              # Utility functions
│   │   │   ├── constants.ts       # Application constants
│   │   │   ├── helpers.ts         # Helper functions
│   │   │   └── performance.ts     # Performance monitoring
│   │   ├── 📁 theme/              # Chakra UI theme
│   │   └── 📁 pages/              # Page components
│   ├── 📁 server/                 # Backend server code
│   │   ├── actions.ts             # Server actions
│   │   ├── queries.ts             # Database queries
│   │   ├── middleware.ts          # Security middleware
│   │   └── webhooks.ts            # Webhook handlers
│   └── 📁 test/                   # Test utilities and setup
├── 📁 e2e/                       # End-to-end tests
├── 📁 scripts/                   # Build and utility scripts
├── main.wasp                     # Wasp configuration
├── schema.prisma                 # Database schema
└── package.json                  # Dependencies and scripts
```

## 🧪 Testing

### Unit Tests
```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### End-to-End Tests
```bash
# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui
```

## 📊 Performance & Monitoring

### Bundle Analysis
```bash
# Analyze bundle size
node scripts/analyze-bundle.js

# Run with webpack analyzer
node scripts/analyze-bundle.js --webpack
```

### Performance Monitoring
The application includes comprehensive performance monitoring:
- **Core Web Vitals** tracking (CLS, FID, FCP, LCP, TTFB)
- **Custom metrics** for user interactions
- **Error boundary** reporting
- **Real-time performance** alerts

## 🔒 Security Features

- **Rate Limiting**: API endpoints protected with configurable rate limits
- **CSRF Protection**: Cross-site request forgery protection
- **Security Headers**: Comprehensive security headers with Helmet.js
- **Input Validation**: Server-side validation with Zod schemas
- **Error Boundaries**: Graceful error handling and reporting

## 🚀 Deployment

### Production Build
```bash
wasp build
```

### Environment Variables
Ensure all production environment variables are set:
- Database connection string
- API keys (OpenAI, Stripe, Google)
- Security configurations
- Performance monitoring endpoints

## 📚 Development Guide

### Code Organization
- **Components**: Maximum 300 lines per file
- **Hooks**: Reusable logic extraction
- **Utils**: Shared utility functions
- **Constants**: Centralized configuration

### Best Practices
- **TypeScript**: Full type safety
- **Error Handling**: Comprehensive error boundaries
- **Performance**: Lazy loading and code splitting
- **Testing**: Unit and E2E test coverage
- **Security**: Input validation and sanitization

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Development Setup
```bash
# Install dependencies
npm install

# Start development server
wasp start

# Run tests
npm run test

# Check code quality
npm run lint
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **[Wasp](https://wasp.sh)** - Full-stack framework with 10x less boilerplate
- **[Chakra UI](https://chakra-ui.com/)** - Simple, modular, and accessible component library
- **[OpenAI](https://openai.com/)** - AI-powered content generation
- **[Stripe](https://stripe.com/)** - Payment processing infrastructure
- **Lightning Network** - Bitcoin payment integration

## 📞 Support

- **Help Center**: [careerdart.com/help](https://careerdart.com/help)
- **Issues**: [GitHub Issues](https://github.com/yonasnh/careerdart/issues)
- **Email**: <EMAIL>
- **Privacy**: <EMAIL>
- **Legal**: <EMAIL>

## ⚠️ Important Disclaimers

### AI-Generated Content
- **Accuracy**: AI-generated content is for guidance only and should be reviewed and customized before use
- **Responsibility**: Users are responsible for verifying the accuracy and appropriateness of all AI-generated content
- **Professional Review**: We recommend having AI-generated resumes and cover letters reviewed by career professionals
- **No Guarantees**: CareerDart does not guarantee job placement or interview success

### Data Privacy
- **User Data**: We prioritize user privacy and data security
- **AI Processing**: Content may be processed by third-party AI services (OpenAI) for generation purposes
- **Data Retention**: See our Privacy Policy for detailed information about data handling

---

<div align="center">
  <strong>🎯 CareerDart - Navigate Your Career Journey with AI-Powered Intelligence</strong>
  <br>
  <em>Built with ❤️ using Wasp, React, and TypeScript</em>
</div>