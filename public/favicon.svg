<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle with Gradient -->
  <defs>
    <linearGradient id="dartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3182ce;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main Background Circle -->
  <circle cx="16" cy="16" r="15" fill="url(#dartGradient)" stroke="white" stroke-width="1"/>
  
  <!-- Career Trajectory Path -->
  <path d="M6 26 Q16 16 26 6" stroke="rgba(255,255,255,0.4)" stroke-width="1.5" fill="none" stroke-linecap="round"/>
  
  <!-- Dart <PERSON> -->
  <g>
    <!-- Dart Body -->
    <path d="M20 12 L26 6 L24 4 L18 10 L16 8 L14 10 L20 16 L22 14 L20 12 Z" fill="white" stroke="rgba(255,255,255,0.3)" stroke-width="0.3"/>
    <!-- Dart Tip -->
    <circle cx="25" cy="7" r="1.2" fill="white"/>
    <!-- Dart Fletching -->
    <path d="M14 10 L12 8 L14 12 L16 10 Z" fill="rgba(255,255,255,0.8)"/>
  </g>
  
  <!-- Progress Dots -->
  <circle cx="10" cy="22" r="1" fill="white" opacity="0.8"/>
  <circle cx="16" cy="16" r="0.8" fill="white" opacity="0.6"/>
  <circle cx="22" cy="10" r="0.6" fill="white" opacity="0.4"/>
</svg>
