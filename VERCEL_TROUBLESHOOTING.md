# Vercel Deployment Troubleshooting for CareerDart

## Issue: Wasp Installation Fails in Vercel Build Environment

### Problem
The error `sh: line 1: wasp: command not found` indicates that Wasp is not available in the Vercel build environment.

### Root Cause
Vercel's build environment doesn't have <PERSON>p pre-installed, and the installation process may fail due to:
- Network restrictions
- Permission issues
- Missing system dependencies
- Architecture compatibility

## Solutions

### Solution 1: Pre-build Locally (Recommended)

This approach builds the application locally and deploys the static assets.

#### Step 1: Build Locally
```bash
# Make the script executable
chmod +x scripts/build-local-for-vercel.sh

# Run the local build
./scripts/build-local-for-vercel.sh
```

#### Step 2: Update Vercel Configuration
```bash
# Use the static configuration
cp vercel-static.json vercel.json
```

#### Step 3: Commit and Deploy
```bash
# Add the build output to git
git add dist/
git add vercel.json
git commit -m "Add pre-built assets for Vercel deployment"
git push origin main

# Deploy to Vercel
vercel --prod
```

### Solution 2: Use GitHub Actions for Building

Create a GitHub Action that builds with <PERSON>p and deploys to Vercel.

#### Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Vercel
on:
  push:
    branches: [main]
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install Wasp
        run: curl -sSL https://get.wasp-lang.dev/installer.sh | sh
        
      - name: Add Wasp to PATH
        run: echo "$HOME/.local/bin" >> $GITHUB_PATH
        
      - name: Build application
        run: |
          npm install
          wasp build
          
      - name: Deploy to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

### Solution 3: Use Docker-based Deployment

Deploy using a Docker container that has Wasp pre-installed.

#### Create `Dockerfile.vercel`:
```dockerfile
FROM node:18-alpine

# Install dependencies
RUN apk add --no-cache curl bash

# Install Wasp
RUN curl -sSL https://get.wasp-lang.dev/installer.sh | sh
ENV PATH="/root/.local/bin:$PATH"

WORKDIR /app
COPY . .

RUN npm install
RUN wasp build

# Copy build output
RUN cp -r .wasp/build/web-app/build/* /app/dist/

EXPOSE 3000
CMD ["npm", "start"]
```

### Solution 4: Fix Wasp Installation in Build Script

If you want to continue with the current approach, try these improvements:

#### Update the build script with better error handling:
```bash
# Add to scripts/build-for-vercel.sh
export WASP_TELEMETRY_DISABLE=1
export CI=true

# Try multiple installation methods
install_wasp() {
    echo "Attempting Wasp installation method $1..."
    case $1 in
        1)
            curl -sSL https://get.wasp-lang.dev/installer.sh | sh
            ;;
        2)
            wget -qO- https://get.wasp-lang.dev/installer.sh | sh
            ;;
        3)
            # Direct binary download
            mkdir -p $HOME/.local/bin
            curl -L "https://github.com/wasp-lang/wasp/releases/download/v0.15.0/wasp-linux-x86_64.tar.gz" | tar -xz -C $HOME/.local/bin
            ;;
    esac
}

for method in 1 2 3; do
    if install_wasp $method; then
        export PATH="$HOME/.local/bin:$PATH"
        if command -v wasp &> /dev/null; then
            echo "Wasp installation successful with method $method"
            break
        fi
    fi
    echo "Method $method failed, trying next..."
done
```

## Environment Variables for Vercel

Make sure these are set in your Vercel project:

### Required Variables:
- `DATABASE_URL` - Your Neon PostgreSQL connection string
- `OPENAI_API_KEY` - Your OpenAI API key
- `GOOGLE_CLIENT_ID` - Google OAuth client ID
- `GOOGLE_CLIENT_SECRET` - Google OAuth client secret
- `SENDGRID_API_KEY` - SendGrid API key
- `STRIPE_KEY` - Stripe secret key
- `PRODUCT_PRICE_ID` - Stripe price ID
- `PRODUCT_CREDITS_PRICE_ID` - Stripe credits price ID

### Application URLs:
- `WASP_WEB_CLIENT_URL` - Your Vercel app URL (e.g., https://careerdart.vercel.app)
- `WASP_SERVER_URL` - Same as above

## Testing Your Deployment

After deployment, test these endpoints:
- `https://your-app.vercel.app` - Main application
- `https://your-app.vercel.app/api/health` - API health check
- `https://your-app.vercel.app/stripe-webhook` - Stripe webhook

## Common Issues and Fixes

### 1. Build Timeout
- Use pre-built assets (Solution 1)
- Optimize build process
- Use Vercel Pro for longer build times

### 2. Memory Issues
- Reduce bundle size
- Use code splitting
- Optimize dependencies

### 3. Database Connection Issues
- Verify Neon connection string
- Check environment variables
- Test database connectivity

### 4. API Routes Not Working
- Check vercel.json routing configuration
- Verify API endpoints
- Check CORS settings

## Getting Help

If you're still having issues:

1. **Check Vercel Build Logs**: Go to your Vercel dashboard and check the build logs
2. **Test Locally**: Run `./scripts/build-local-for-vercel.sh` to test the build process
3. **Verify Environment**: Ensure all environment variables are set correctly
4. **Contact Support**: Reach out to Vercel support or the Wasp community

## Recommended Approach

For the most reliable deployment, we recommend **Solution 1 (Pre-build Locally)**:

1. It eliminates build environment issues
2. Faster deployment times
3. More predictable results
4. Easier debugging

Run these commands to implement it:

```bash
chmod +x scripts/build-local-for-vercel.sh
./scripts/build-local-for-vercel.sh
cp vercel-static.json vercel.json
git add dist/ vercel.json
git commit -m "Add pre-built assets for Vercel"
git push origin main
vercel --prod
```
