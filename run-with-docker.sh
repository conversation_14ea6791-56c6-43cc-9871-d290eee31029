#!/bin/bash

echo "==================================================================="
echo "RUNNING WITH DOCKER"
echo "This script will start the application using Docker."
echo "Make sure Docker Desktop is running before executing this script."
echo "==================================================================="
echo ""

# Start Docker containers
echo "Starting Docker containers..."
docker-compose up -d

# Wait for the containers to start
echo "Waiting for containers to start..."
sleep 10

# Apply database migrations
echo "Applying database migrations..."
docker-compose exec app wasp db migrate-dev

# Show logs
echo "Application is running!"
echo "Frontend: http://localhost:3000"
echo "Backend: http://localhost:3001"
echo ""
echo "Showing logs (press Ctrl+C to stop viewing logs):"
docker-compose logs -f
