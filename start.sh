#!/bin/bash

echo "==================================================================="
echo "RUNNING IN LIMITED MODE WITHOUT WASP"
echo "This script will start Prisma Studio and the frontend using Vite."
echo "However, without <PERSON><PERSON>, the backend server will not be available."
echo "You can browse the database using Prisma Studio and see the frontend UI,"
echo "but API calls will fail and functionality will be limited."
echo "==================================================================="
echo ""

# Set environment variables
export DATABASE_URL=postgresql://ynebro@localhost:5432/careergpt

# Start Prisma Studio in the background
echo "Starting Prisma Studio..."
npx prisma studio &
PRISMA_PID=$!

# Start the frontend using Vite
echo "Starting frontend using Vite..."
cd src/client
npx vite &
VITE_PID=$!

# Trap to kill background processes when the script is terminated
trap "kill $PRISMA_PID $VITE_PID; exit" INT TERM EXIT

# Keep the script running
echo "Application is running in limited mode. Press Ctrl+C to stop."
echo "Prisma Studio: http://localhost:5555"
echo "Frontend: http://localhost:5173"
wait
