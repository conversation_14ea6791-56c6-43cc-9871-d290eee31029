datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                   Int              @id @default(autoincrement())
  username             String           @unique
  email                String           @unique
  hasPaid              Boolean          @default(false)
  isUsingLn            Boolean          @default(false)
  gptModel             String           @default("gpt-4o-mini")
  datePaid             DateTime?
  stripeId             String?
  checkoutSessionId    String?
  subscriptionStatus   String?
  notifyPaymentExpires Boolean          @default(false)
  credits              Int              @default(3)
  yearsOfExperience    Int?             @default(0)

  // Profile settings
  profileImageUrl      String?
  bio                  String?

  // User preferences
  darkMode             Boolean          @default(false)
  wordCountDisplay     Boolean          @default(true)
  autoSave             Boolean          @default(true)

  // Notification preferences
  jobReminders         Boolean          @default(true)
  featureUpdates       <PERSON><PERSON><PERSON>          @default(true)
  subscriptionReminders Boolean         @default(true)
  marketingEmails      Boolean          @default(false)

  // Account settings
  lastLoginAt          DateTime?
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @default(now()) @updatedAt

  letters              CoverLetter[]
  jobs                 Job[]
  lnData               LnData?
  lnPayments           LnPayment[]
  resumes              Resume[]
  jobApplications      JobApplication[]
  learningProgress     LearningProgress[]
  userBadges           UserBadge[]
  interviewQuestionSets InterviewQuestionSet[]
  savedInterviewQuestions SavedInterviewQuestion[]
  practiceAnswers      PracticeAnswer[]
}

model LnData {
  id        String    @id @default(uuid())
  user      User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    Int?      @unique
  loginUrl  String?   @unique
  k1Hash    String    @unique
  token     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

// type LightningInvoice = {
//   status: string;
//   successAction: {
//     tag: string;
//     message: string;
//   };
//   verify: string;
//   routes: any[]; // You can replace this with a more specific type if needed
//   pr: string;
// };

model LnPayment {
  pr        String    @id @unique
  status    String
  settled   Boolean   @default(false)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    Int
  amount    Int?
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model CoverLetter {
  id         String    @id @default(uuid())
  title      String
  content    String
  tokenUsage Int
  job        Job       @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId      String
  user       User?     @relation(fields: [userId], references: [id])
  userId     Int?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime? @updatedAt
}

model Job {
  id          String           @id @default(uuid())
  title       String
  company     String
  location    String
  description String
  coverLetter CoverLetter[]
  user        User?            @relation(fields: [userId], references: [id])
  userId      Int?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  isCompleted Boolean          @default(false)
  applications JobApplication[]
}

model JobApplication {
  id            String    @id @default(uuid())
  job           Job       @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId         String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId        Int
  dateApplied   DateTime
  status        String    // "Saved", "Applied", "Interview", "Offer", "Rejected"
  notes         String?
  salary        String?
  contactPerson String?
  contactEmail  String?
  followUpDate  DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Resume {
  id          String   @id @default(uuid())
  title       String
  templateId  String?
  personalInfo Json
  summary     String
  experience  Json
  education   Json
  skills      Json
  certifications Json?
  fileData    Json?
  user        User     @relation(fields: [userId], references: [id])
  userId      Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
}

model LearningProgress {
  id           String   @id @default(uuid())
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       Int
  resourceId   String   // ID of the learning resource
  resourceType String   // "video", "article", etc.
  progress     Int      @default(0) // 0-100
  completed    Boolean  @default(false)
  timeSpent    Int      @default(0) // in seconds
  lastAccessed DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@unique([userId, resourceId])
  @@index([userId])
}

model Badge {
  id          String      @id @default(uuid())
  name        String      @unique
  description String
  icon        String      // Icon name or emoji
  category    String      // "learning", "experience", "achievement"
  criteria    Json        // Criteria for earning the badge
  color       String      @default("purple")
  createdAt   DateTime    @default(now())
  userBadges  UserBadge[]
}

model UserBadge {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    Int
  badge     Badge    @relation(fields: [badgeId], references: [id], onDelete: Cascade)
  badgeId   String
  earnedAt  DateTime @default(now())

  @@unique([userId, badgeId])
  @@index([userId])
}

model InterviewQuestionSet {
  id              String   @id @default(uuid())
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId          Int
  jobId           String
  jobTitle        String
  company         String
  jobDescription  String
  questions       Json     // Array of questions with category, question, tips, answer, references
  generatedAt     DateTime @default(now())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([userId])
  @@index([userId, jobId])
}

model SavedInterviewQuestion {
  id          String   @id @default(uuid())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      Int
  questionType String  // "common" or "generated"
  questionId  String? // For common questions
  questionData Json    // Full question data for generated questions
  notes       String?
  savedAt     DateTime @default(now())

  @@index([userId])
  @@unique([userId, questionType, questionId])
}

model PracticeAnswer {
  id           String   @id @default(uuid())
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       Int
  questionType String   // "common" or "generated"
  questionData Json     // Full question data
  userAnswer   String
  notes        String?
  savedAt      DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([userId])
}
