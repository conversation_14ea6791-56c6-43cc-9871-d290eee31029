# Production Environment Variables for CareerDart
# Copy this file to .env.production and fill in your actual values

# Application URLs (replace with your actual Vercel domain)
WASP_WEB_CLIENT_URL=https://your-careerdart-app.vercel.app
WASP_SERVER_URL=https://your-careerdart-app.vercel.app

# Database (Neon PostgreSQL)
DATABASE_URL=postgresql://username:<EMAIL>/neondb?sslmode=require

# AI Services
OPENAI_API_KEY=your_openai_api_key_here

# Authentication (Google OAuth)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Email Service (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key
SEND_EMAILS_IN_DEVELOPMENT=false

# Payment Processing (Stripe)
STRIPE_KEY=your_stripe_secret_key
PRODUCT_PRICE_ID=your_stripe_price_id
PRODUCT_CREDITS_PRICE_ID=your_credits_price_id

# Application Settings
NODE_ENV=production
