# CareerDart Deployment Guide - Vercel + Neon

This guide will help you deploy Career<PERSON>art to Vercel with Neon as the PostgreSQL database.

## Prerequisites

- [Vercel Account](https://vercel.com)
- [Neon Account](https://neon.tech)
- [GitHub Repository](https://github.com) (for automatic deployments)
- All required API keys (OpenAI, Google OAuth, Stripe, SendGrid)

## Step 1: Set Up Neon Database

1. **Create Neon Project**
   - Go to [Neon Console](https://console.neon.tech/)
   - Create a new project named "careerdart"
   - Select your preferred region
   - Choose PostgreSQL 15+

2. **Get Connection String**
   - Copy the connection string from your Neon dashboard
   - It should look like: `postgresql://username:<EMAIL>/neondb?sslmode=require`

3. **Test Locally (Optional)**
   ```bash
   # Update your .env.server with Neon URL
   DATABASE_URL=your_neon_connection_string
   
   # Test the connection
   wasp db migrate-dev
   ```

## Step 2: Prepare for Deployment

1. **Make Build Script Executable**
   ```bash
   chmod +x scripts/build-for-vercel.sh
   ```

2. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

3. **Login to Vercel**
   ```bash
   vercel login
   ```

## Step 3: Configure Environment Variables

1. **Create Production Environment File**
   ```bash
   cp .env.production.example .env.production
   ```

2. **Fill in your actual values in `.env.production`:**
   - `DATABASE_URL`: Your Neon connection string
   - `WASP_WEB_CLIENT_URL`: Your Vercel app URL (e.g., https://careerdart.vercel.app)
   - `WASP_SERVER_URL`: Same as above
   - `OPENAI_API_KEY`: Your OpenAI API key
   - `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET`: Google OAuth credentials
   - `SENDGRID_API_KEY`: SendGrid API key
   - `STRIPE_KEY`, `PRODUCT_PRICE_ID`, `PRODUCT_CREDITS_PRICE_ID`: Stripe keys

## Step 4: Deploy to Vercel

### Option A: Deploy via CLI

1. **Initial Deployment**
   ```bash
   vercel
   ```
   - Follow the prompts
   - Choose your project settings
   - Set build command: `npm run vercel-build`
   - Set output directory: `.wasp/build/web-app/build`

2. **Set Environment Variables**
   ```bash
   # Set each environment variable
   vercel env add DATABASE_URL
   vercel env add WASP_WEB_CLIENT_URL
   vercel env add WASP_SERVER_URL
   vercel env add OPENAI_API_KEY
   vercel env add GOOGLE_CLIENT_ID
   vercel env add GOOGLE_CLIENT_SECRET
   vercel env add SENDGRID_API_KEY
   vercel env add STRIPE_KEY
   vercel env add PRODUCT_PRICE_ID
   vercel env add PRODUCT_CREDITS_PRICE_ID
   ```

3. **Deploy to Production**
   ```bash
   vercel --prod
   ```

### Option B: Deploy via GitHub Integration

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Add Vercel deployment configuration"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository
   - Configure build settings:
     - Build Command: `npm run vercel-build`
     - Output Directory: `.wasp/build/web-app/build`

3. **Set Environment Variables in Vercel Dashboard**
   - Go to Project Settings → Environment Variables
   - Add all variables from your `.env.production` file

## Step 5: Post-Deployment

1. **Run Database Migrations**
   ```bash
   # If not done automatically during build
   vercel env pull .env.local
   wasp db migrate-deploy
   ```

2. **Update Google OAuth Settings**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Update authorized redirect URIs to include your Vercel domain

3. **Update Stripe Webhook URLs**
   - Update webhook endpoints in Stripe dashboard
   - Point to: `https://your-app.vercel.app/stripe-webhook`

4. **Test Your Deployment**
   - Visit your Vercel URL
   - Test user registration/login
   - Test core features (resume creation, cover letters, etc.)

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Vercel build logs
   - Ensure all environment variables are set
   - Verify Wasp installation in build process

2. **Database Connection Issues**
   - Verify Neon connection string
   - Check if database is accessible from Vercel
   - Ensure SSL mode is enabled

3. **API Errors**
   - Check environment variables are properly set
   - Verify API keys are valid
   - Check CORS settings

### Useful Commands

```bash
# View deployment logs
vercel logs

# Check environment variables
vercel env ls

# Redeploy
vercel --prod

# Local development with production env
vercel dev
```

## Monitoring and Maintenance

1. **Set up monitoring** in Vercel dashboard
2. **Monitor database usage** in Neon console
3. **Set up alerts** for errors and performance issues
4. **Regular backups** (Neon provides automatic backups)

## Scaling Considerations

- **Neon**: Upgrade to Pro tier for better performance
- **Vercel**: Consider Pro plan for better performance and analytics
- **CDN**: Vercel provides global CDN automatically
- **Database**: Use Neon's read replicas for better performance
