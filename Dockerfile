FROM node:18

# Install dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    libpq-dev \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Wasp
RUN curl -sSL https://get.wasp.sh/installer.sh | VERSION=0.15.0 bash

# Set up working directory
WORKDIR /app

# Copy project files
COPY . .

# Set environment variables
ENV PATH="/root/.local/bin:${PATH}"
ENV DATABASE_URL="********************************************/careergpt"
ENV WASP_SERVER_URL="http://localhost:3001"

# Expose ports
EXPOSE 3000 3001 5432

# Start the application
CMD ["wasp", "start"]
